import { getCLS, getFID, getFCP, getLCP, getTTFB } from "web-vitals";

// Performance monitoring with Web Vitals
const sendToAnalytics = (metric) => {
  // In production, you would send this to your analytics service
  if (process.env.NODE_ENV === "development") {
    console.log("Web Vital:", metric);
  }

  // Example: Send to Google Analytics 4
  if (window.gtag) {
    window.gtag("event", metric.name, {
      value: Math.round(
        metric.name === "CLS" ? metric.value * 1000 : metric.value
      ),
      event_category: "Web Vitals",
      event_label: metric.id,
      non_interaction: true,
    });
  }
};

// Initialize Web Vitals tracking
export const initWebVitals = () => {
  getCLS(sendToAnalytics);
  getFID(sendToAnalytics);
  getFCP(sendToAnalytics);
  getLCP(sendToAnalytics);
  getTTFB(sendToAnalytics);
};

// Performance optimization utilities
export const performanceUtils = {
  // Preload critical resources
  preloadResource: (href, as = "script", crossorigin = false) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.href = href;
    link.as = as;
    if (crossorigin) {
      link.crossOrigin = "anonymous";
    }
    document.head.appendChild(link);
  },

  // Prefetch resources for next navigation
  prefetchResource: (href) => {
    const link = document.createElement("link");
    link.rel = "prefetch";
    link.href = href;
    document.head.appendChild(link);
  },

  // Critical CSS injection
  injectCriticalCSS: (css) => {
    const style = document.createElement("style");
    style.textContent = css;
    document.head.appendChild(style);
  },

  // Lazy load non-critical resources
  loadResourceWhenIdle: (callback) => {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(callback, { timeout: 2000 });
    } else {
      setTimeout(callback, 1);
    }
  },

  // Report long tasks
  observeLongTasks: () => {
    if ("PerformanceObserver" in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn("Long task detected:", entry);
          }
        });
      });
      observer.observe({ entryTypes: ["longtask"] });
    }
  },

  // Measure custom metrics
  measureCustomMetric: (name, startTime, endTime) => {
    if ("performance" in window && "measure" in performance) {
      performance.measure(name, startTime, endTime);
      const measure = performance.getEntriesByName(name)[0];
      console.log(`${name}: ${measure.duration}ms`);
      return measure.duration;
    }
  },
};

const webVitalsUtils = { initWebVitals, performanceUtils };

export default webVitalsUtils;
