/* Header styles */
.c-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(1.125rem);
  backdrop-filter: blur(1.125rem);
  border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  padding: 1rem 2rem;
}

.c-header__body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.c-logo {
  display: block;
}

.c-logo img {
  height: 28px;
  width: auto;
  max-width: 121px;
  filter: brightness(1.1);
}

.c-burger__action {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: none;
  border: none;
}

.c-burger__wrap {
  display: flex;
  flex-direction: column;
}

.c-burger__wrap span {
  display: block;
  width: 30px;
  height: 2px;
  background-color: #000;
  margin: 3px 0;
  transition: all 0.3s ease;
}

/* Menu overlay */
.c-menu__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1020;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.c-menu__overlay.active {
  height: 100%;
  opacity: 1;
  pointer-events: all;
}

.c-menu__close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  color: #fff;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1031; /* Ensure it's above other menu content */
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.c-menu__close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.c-menu__close-button:active {
  transform: scale(0.95);
}

/* Menu styles */
.c-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ff4338;
  z-index: 1030;
  transform: translateY(-100%);
  transition: transform 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-menu.is-open {
  transform: translateY(0);
}

.c-navigation__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.c-navigation__list li {
  margin: 1rem 0;
}

.c-navigation__list li a {
  color: #fff;
  font-size: 2.5rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.c-navigation__list li a:hover {
  opacity: 0.8;
}

/* Progress bar */
.c-header__progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  z-index: 1001;
}

.c-header__progress div {
  height: 100%;
  background-color: #ff4338;
  width: 0;
  transition: width 0.3s ease;
}

/* Header static */
.c-header--static {
  position: fixed;
  background-color: transparent;
  transition: background-color 0.3s ease;
  z-index: 1050;
}

body.menu-open {
  overflow: hidden;
}

/* Responsive design with proper units */

/* Extra Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 22px;
    max-width: 90px;
  }

  .c-burger__wrap span {
    width: 20px;
    height: 2px;
    margin: 2px 0;
  }

  .c-navigation__list li a {
    font-size: 1.8rem;
  }

  .c-menu__close-button {
    top: 1rem;
    right: 1rem;
    width: 32px;
    height: 32px;
    font-size: 1.25rem;
  }
}

/* Small Mobile (481px - 600px) */
@media (min-width: 481px) and (max-width: 600px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 0.875rem 1.25rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 24px;
    max-width: 100px;
  }

  .c-burger__wrap span {
    width: 24px;
    height: 2px;
    margin: 2.5px 0;
  }

  .c-navigation__list li a {
    font-size: 2rem;
  }

  .c-menu__close-button {
    top: 1rem;
    right: 1rem;
    width: 36px;
    height: 36px;
    font-size: 1.5rem;
  }
}

/* Large Mobile/Small Tablet (601px - 768px) */
@media (min-width: 601px) and (max-width: 768px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1rem 1.5rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 26px;
    max-width: 110px;
  }

  .c-burger__wrap span {
    width: 26px;
    height: 2px;
    margin: 3px 0;
  }

  .c-navigation__list li a {
    font-size: 2.1rem;
  }

  .c-menu__close-button {
    top: 1rem;
    right: 1rem;
    width: 38px;
    height: 38px;
    font-size: 1.75rem;
  }
}

/* Tablet Portrait (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1rem 2rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 26px;
    max-width: 115px;
  }

  .c-desktop-navigation__item {
    margin: 0 0.75rem;
  }

  .c-desktop-navigation__item a {
    font-size: 0.9rem;
  }

  .c-navigation__list {
    flex-direction: row;
  }

  .c-navigation__list li {
    margin: 0 1.5rem;
  }
}

/* Small Desktop (1025px - 1200px) */
@media (min-width: 1025px) and (max-width: 1200px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1rem 2.5rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 27px;
    max-width: 118px;
  }

  .c-desktop-navigation__item {
    margin: 0 1rem;
  }

  .c-desktop-navigation__item a {
    font-size: 0.95rem;
  }
}

/* Large Desktop (1201px - 1440px) */
@media (min-width: 1201px) and (max-width: 1440px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1rem 3rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 28px;
    max-width: 121px;
  }

  .c-desktop-navigation__item {
    margin: 0 1.25rem;
  }

  .c-desktop-navigation__item a {
    font-size: 1rem;
  }
}

/* Desktop Navigation Styles */
.c-header__desktop-nav {
  display: none; /* Hidden by default on mobile */
}

.c-desktop-navigation__list {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.c-desktop-navigation__item {
  margin: 0 1rem;
}

.c-desktop-navigation__item a {
  color: #000;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: color 0.3s ease;
  position: relative;
}

.c-desktop-navigation__item a:hover {
  color: #ff4338;
}

.c-desktop-navigation__item a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: #ff4338;
  transition: width 0.3s ease;
}

.c-desktop-navigation__item a:hover::after {
  width: 100%;
}

/* Extra Large Desktop (1441px+) */
@media (min-width: 1441px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1rem 3.5rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 30px;
    max-width: 130px;
  }

  .c-desktop-navigation__item {
    margin: 0 1.5rem;
  }

  .c-desktop-navigation__item a {
    font-size: 1.1rem;
  }
}

/* Show desktop nav and hide burger on tablet and desktop */
@media (min-width: 769px) {
  .c-header__desktop-nav {
    display: block;
  }

  .c-header__action {
    display: none; /* Hide burger menu on desktop */
  }

  .c-header__body {
    justify-content: space-between;
  }
}

/* Ensure burger is visible on mobile and small tablets */
@media (max-width: 768px) {
  .c-header__desktop-nav {
    display: none;
  }

  .c-header__action {
    display: block;
  }

  /* Improve mobile menu spacing */
  .c-navigation__list li a {
    padding: 0.5rem 0;
    display: block;
  }
}

/* Ultra-wide displays (1920px+) */
@media (min-width: 1920px) {
  .c-header {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 1.25rem 4rem;
    border-radius: 0;
    border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.04);
  }

  .c-header__body {
    padding: 0;
  }

  .c-logo img {
    height: 32px;
    max-width: 140px;
  }

  .c-desktop-navigation__item {
    margin: 0 2rem;
  }

  .c-desktop-navigation__item a {
    font-size: 1.2rem;
  }
}

/* High DPI displays adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .c-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
