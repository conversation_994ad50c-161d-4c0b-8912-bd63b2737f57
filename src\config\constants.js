// Configuration constants for the Auften website
export const SITE_CONFIG = {
  // Site Information
  name: "Auften",
  tagline: "Designing spaces that inspire!",
  description:
    "Award-winning architecture firm specializing in residential, commercial, and hospitality design.",

  // Contact Information
  contact: {
    phone: "+61 (0) 3 9600 0222",
    email: "<EMAIL>",
    locations: ["Melbourne", "Albury"],
  },

  // Social Media
  social: {
    instagram: "https://www.instagram.com/auftenarchitects/",
  },

  // Navigation Menu Items
  navigation: [
    { path: "/", label: "Home" },
    { path: "/projects", label: "Projects" },
    { path: "/studio", label: "Studio" },
    { path: "/contact", label: "Contact" },
  ],
};

// Animation Configuration
export const ANIMATION_CONFIG = {
  // AOS (Animate On Scroll) Settings
  aos: {
    duration: 800,
    once: true,
    offset: 120,
    delay: 0,
  },

  // Custom Animation Timings
  transitions: {
    fast: "0.2s",
    normal: "0.3s",
    slow: "0.6s",
    extraSlow: "1s",
  },

  // Animation Delays
  delays: {
    none: 0,
    short: 100,
    medium: 200,
    long: 300,
    textAnimation: 500, // For text underline animations
  },
};

// UI Configuration
export const UI_CONFIG = {
  // Breakpoints (must match CSS)
  breakpoints: {
    mobile: "768px",
    tablet: "1024px",
    desktop: "1200px",
  },

  // Grid Settings
  grid: {
    maxWidth: "1200px",
    gutter: "2rem",
    halfGutter: "1rem",
  },

  // Component Sizes
  sizes: {
    headerHeight: "80px",
    mobileHeaderHeight: "70px",
    footerMinHeight: "300px",
  },
};

// Performance Configuration
export const PERFORMANCE_CONFIG = {
  // Image Loading
  lazyLoading: {
    threshold: 0.1,
    rootMargin: "50px",
  },

  // Video Settings
  video: {
    preload: "auto",
    muted: true,
    autoPlay: true,
    playsInline: true,
  },

  // Intersection Observer
  intersectionObserver: {
    threshold: 0.3,
    rootMargin: "0px",
  },
};

// Content Configuration
export const CONTENT_CONFIG = {
  // Home Page
  homepage: {
    projectsToShow: 3,
    foundersToShow: 2,
    introVideoDuration: 3000, // 3 seconds
  },

  // Projects Page
  projectsPage: {
    defaultFilter: "All",
    projectsPerPage: 12,
  },
};

// Asset Paths
export const ASSETS = {
  logos: {
    black: "/Logos/SVG/Auften Black Logo.svg",
    white: "/Logos/SVG/Auften WhiteLogo.svg",
    red: "/Logos/SVG/Auften Red Logo.svg",
    symbolBlack: "/Logos/SVG/Auften Black Symbol.svg",
    symbolWhite: "/Logos/SVG/Auften White Symbol.svg",
    symbolRed: "/Logos/SVG/Auften Red Symbol.svg",
  },

  icons: {
    favicon: "/favicon.ico",
    appleTouchIcon: "/logo192.png",
  },
};

// Intro Videos Configuration
export const INTRO_VIDEOS = [
  {
    url: "https://player.vimeo.com/progressive_redirect/playback/715426008/rendition/1080p/file.mp4?loc=external&signature=22585c4a7285c5d68e08ed071c036c22c9da89e4d7f5573d8b228c968fae5662",
    outro: "Designing spaces that inspire!",
  },
  {
    url: "https://player.vimeo.com/progressive_redirect/playback/715413191/rendition/1080p/file.mp4?loc=external&signature=c821fbbbc85aa66a0dd46213ab211c04b10fee984cb9c995308ce1d92a15431a",
    outro: "Designing spaces that inspire!",
  },
  {
    url: "https://player.vimeo.com/progressive_redirect/playback/713072778/rendition/1080p/file.mp4?loc=external&signature=f67a9ebe1f5adee417ef06aa654c784c92fa4b03918d0c1ed136c6d6d6d8b960",
    outro: "Designing spaces that inspire!",
  },
];

// Theme Configuration
export const THEME = {
  colors: {
    primary: "#FF4338",
    secondary: "#000000",
    white: "#FFFFFF",
    lightGray: "#F5F5F5",
    mediumGray: "#E0E0E0",
    darkGray: "#666666",
    black: "#000000",
  },

  typography: {
    fontFamily: "Arial, sans-serif",
    fontWeights: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.7,
    },
  },

  spacing: {
    xs: "0.5rem",
    sm: "1rem",
    md: "2rem",
    lg: "3rem",
    xl: "5rem",
    xxl: "8rem",
  },
};

const CONFIG = {
  SITE_CONFIG,
  ANIMATION_CONFIG,
  UI_CONFIG,
  PERFORMANCE_CONFIG,
  CONTENT_CONFIG,
  ASSETS,
  INTRO_VIDEOS,
  THEME,
};

export default CONFIG;
