@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Domine:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap");

:root {
  --primary-color: #ff4338;
  --text-color: #000000;
  --text-light: #666666;
  --background-light: #ffffff;
  --background-gray: #f5f5f5;
  --border-color: #e0e0e0;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: 0; /* Remove default padding */
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-color);
}

img {
  max-width: 100%;
}

button {
  cursor: pointer;
}

.u-h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.u-h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.u-h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.u-pt-xl {
  padding-top: 8rem;
}

/* Add space for the fixed header */
main {
  padding-top: 0;
}
