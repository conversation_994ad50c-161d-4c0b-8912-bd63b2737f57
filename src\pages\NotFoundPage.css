.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
  text-align: center;
}

.not-found-content {
  max-width: 600px;
}

.not-found-container h1 {
  font-size: 6rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.not-found-container h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.not-found-container p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.back-to-home-btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: background-color 0.3s ease;
  text-decoration: none;
}

.back-to-home-btn:hover {
  background-color: #c42828;
  text-decoration: none;
}
