/* Contact Page styles */
.contact-page {
  padding-bottom: 4rem;
}

.contact-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  margin-top: 2rem;
}

.contact-info {
  flex: 1;
  min-width: 300px;
}

.contact-form-container {
  flex: 1;
  min-width: 300px;
}

.office-info,
.contact-email,
.social-links {
  margin-bottom: 2rem;
}

.office-info p,
.contact-email p {
  margin-bottom: 0.5rem;
}

.office-info a,
.contact-email a {
  color: #000;
  text-decoration: none;
  transition: color 0.3s ease;
}

.office-info a:hover,
.contact-email a:hover {
  color: #ff4338;
}

.social-links .c-social__list {
  display: flex;
  list-style: none;
  padding: 0;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.social-links .c-social__list-item a {
  color: #000;
  text-decoration: none;
  transition: color 0.3s ease;
}

.social-links .c-social__list-item a:hover {
  color: #ff4338;
}

/* Form styles */
.contact-form {
  background-color: #f5f5f5;
  padding: 2rem;
  border-radius: 4px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #ff4338;
  outline: none;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #ff4338;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background-color: #e63c31;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Form validation styles */
.form-group.has-error input,
.form-group.has-error textarea {
  border-color: #ff4338;
}

.error-message {
  color: #ff4338;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: block;
}

.form-success-message {
  background-color: #e0f7e5;
  color: #1e7e34;
  padding: 2rem;
  border-radius: 4px;
  text-align: center;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .contact-grid {
    flex-direction: column;
  }
}
