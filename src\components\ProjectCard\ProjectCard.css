/* Project card styles */
.c-project-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 400px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.c-project-card.featured {
  height: 500px;
}

.c-project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.c-project-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
  color: white;
}

.c-project-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.c-project-card__image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0.3) 58.94%,
    rgba(0, 0, 0, 0.5) 100%
  );
  z-index: 1;
}

.c-project-card__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1.5rem;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.c-project-card__meta {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.c-project-card__heading {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0;
  transition: transform 0.3s ease;
}

.c-project-card:hover .c-project-card__heading {
  transform: translateY(5px);
}

@media (max-width: 768px) {
  .c-project-card,
  .c-project-card.featured {
    height: 350px;
  }
}
