import React, { useEffect, memo } from "react";
import { <PERSON> } from "react-router-dom";
import AOS from "aos";
import "./NotFoundPage.css";

const NotFoundPage = memo(() => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  return (
    <div className="not-found-container">
      <div className="not-found-content" data-aos="fade-up">
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>The page you are looking for doesn't exist or has been moved.</p>
        <Link to="/" className="back-to-home-btn">
          Back to Home
        </Link>
      </div>
    </div>
  );
});

NotFoundPage.displayName = "NotFoundPage";

export default NotFoundPage;
