/* CTA card styles */
.c-cta-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.c-cta-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.c-cta-card.red::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 67, 56, 0.5);
  z-index: 1;
}

.c-cta-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
}

.c-cta-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.c-cta-card__image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 50%
  );
  z-index: 1;
}

.c-cta-card__content {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 2rem;
  z-index: 2;
  transition: transform 0.3s ease;
}

.c-cta-card:hover .c-cta-card__content {
  transform: translateY(-10px);
}

@media (max-width: 768px) {
  .c-cta-card {
    height: 350px;
  }

  .c-cta-card__heading {
    font-size: 1.75rem !important;
  }
}
