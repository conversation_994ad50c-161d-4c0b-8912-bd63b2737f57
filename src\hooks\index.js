// Custom React hooks for the Auften website
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { getRandomItem } from "../utils/helpers";

/**
 * Hook for managing intersection observer
 * @param {Object} options - Intersection observer options
 * @returns {Array} [ref, isIntersecting, observer]
 */
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const elementRef = useRef(null);
  const optionsRef = useRef(options);

  // Update options ref when options change
  optionsRef.current = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observerInstance = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: "50px",
        ...optionsRef.current,
      }
    );

    observerInstance.observe(element);

    return () => {
      observerInstance.disconnect();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return [elementRef, isIntersecting];
};

/**
 * Hook for managing scroll position
 * @returns {Object} { scrollY, scrollDirection }
 */
export const useScrollPosition = () => {
  const [scrollY, setScrollY] = useState(0);
  const [scrollDirection, setScrollDirection] = useState("up");
  const previousScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      setScrollDirection(
        currentScrollY > previousScrollY.current ? "down" : "up"
      );
      setScrollY(currentScrollY);
      previousScrollY.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return { scrollY, scrollDirection };
};

/**
 * Hook for managing window size
 * @returns {Object} { width, height, isMobile, isTablet, isDesktop }
 */
export const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return {
    ...windowSize,
    isMobile: windowSize.width <= 768,
    isTablet: windowSize.width > 768 && windowSize.width <= 1024,
    isDesktop: windowSize.width > 1024,
  };
};

/**
 * Hook for managing localStorage with state
 * @param {string} key - Storage key
 * @param {*} initialValue - Initial value
 * @returns {Array} [value, setValue]
 */
export const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value) => {
      try {
        const valueToStore =
          value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  return [storedValue, setValue];
};

/**
 * Hook for managing async operations
 * @param {Function} asyncFunction - Async function to execute
 * @returns {Object} { data, loading, error, refetch }
 */
export const useAsync = (asyncFunction) => {
  const [state, setState] = useState({
    data: null,
    loading: true,
    error: null,
  });

  const execute = useCallback(async () => {
    setState({ data: null, loading: true, error: null });

    try {
      const result = await asyncFunction();
      setState({ data: result, loading: false, error: null });
    } catch (error) {
      setState({ data: null, loading: false, error });
    }
  }, [asyncFunction]);

  useEffect(() => {
    execute();
  }, [execute]);

  const refetch = useCallback(() => {
    execute();
  }, [execute]);

  return { ...state, refetch };
};

/**
 * Hook for managing image loading state
 * @param {string} src - Image source URL
 * @returns {Object} { loaded, error, loading }
 */
export const useImageLoad = (src) => {
  const [state, setState] = useState({
    loaded: false,
    error: false,
    loading: true,
  });

  useEffect(() => {
    if (!src) {
      setState({ loaded: false, error: true, loading: false });
      return;
    }

    setState({ loaded: false, error: false, loading: true });

    const img = new Image();

    img.onload = () => {
      setState({ loaded: true, error: false, loading: false });
    };

    img.onerror = () => {
      setState({ loaded: false, error: true, loading: false });
    };

    img.src = src;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  return state;
};

/**
 * Hook for managing video selection (for intro videos)
 * @param {Array} videos - Array of video objects
 * @returns {Object} { selectedVideo, selectRandomVideo }
 */
export const useVideoSelection = (videos = []) => {
  const [selectedVideo, setSelectedVideo] = useState(null);

  const selectRandomVideo = useCallback(() => {
    const randomVideo = getRandomItem(videos);
    setSelectedVideo(randomVideo);
  }, [videos]);

  useEffect(() => {
    if (videos.length > 0) {
      selectRandomVideo();
    }
  }, [videos, selectRandomVideo]);

  return { selectedVideo, selectRandomVideo };
};

/**
 * Hook for managing form state
 * @param {Object} initialValues - Initial form values
 * @returns {Object} { values, errors, handleChange, handleSubmit, reset, isValid }
 */
export const useForm = (initialValues = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});

  const handleChange = useCallback(
    (name, value) => {
      setValues((prev) => ({ ...prev, [name]: value }));

      // Clear error when user starts typing
      if (errors[name]) {
        setErrors((prev) => ({ ...prev, [name]: "" }));
      }
    },
    [errors]
  );

  const handleSubmit = useCallback(
    (onSubmit, validation) => {
      return (event) => {
        event.preventDefault();

        // Run validation if provided
        if (validation) {
          const validationErrors = validation(values);
          setErrors(validationErrors);

          // If there are errors, don't submit
          if (Object.keys(validationErrors).length > 0) {
            return;
          }
        }

        onSubmit(values);
      };
    },
    [values]
  );

  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
  }, [initialValues]);

  const isValid = Object.keys(errors).length === 0;

  return {
    values,
    errors,
    handleChange,
    handleSubmit,
    reset,
    isValid,
  };
};

/**
 * Hook for managing filter state (for projects)
 * @param {Array} items - Array of items to filter
 * @param {string} initialFilter - Initial filter value
 * @returns {Object} { filteredItems, activeFilter, setFilter, categories }
 */
export const useFilter = (items = [], initialFilter = "All") => {
  const [activeFilter, setActiveFilter] = useState(initialFilter);

  const categories = useMemo(() => {
    if (!Array.isArray(items)) return ["All"];

    const uniqueCategories = [...new Set(items.map((item) => item.category))];
    return ["All", ...uniqueCategories];
  }, [items]);

  const filteredItems = useMemo(() => {
    if (!Array.isArray(items)) return [];

    if (activeFilter === "All") {
      return items;
    }

    return items.filter((item) => item.category === activeFilter);
  }, [items, activeFilter]);

  const setFilter = useCallback((filter) => {
    setActiveFilter(filter);
  }, []);

  return {
    filteredItems,
    activeFilter,
    setFilter,
    categories,
  };
};

/**
 * Hook for managing toggle state
 * @param {boolean} initialState - Initial toggle state
 * @returns {Array} [state, toggle, setTrue, setFalse]
 */
export const useToggle = (initialState = false) => {
  const [state, setState] = useState(initialState);

  const toggle = useCallback(() => {
    setState((prev) => !prev);
  }, []);

  const setTrue = useCallback(() => {
    setState(true);
  }, []);

  const setFalse = useCallback(() => {
    setState(false);
  }, []);

  return [state, toggle, setTrue, setFalse];
};

/**
 * Hook for managing debounced value
 * @param {*} value - Value to debounce
 * @param {number} delay - Debounce delay in milliseconds
 * @returns {*} Debounced value
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook for managing previous value
 * @param {*} value - Current value
 * @returns {*} Previous value
 */
export const usePrevious = (value) => {
  const ref = useRef();

  useEffect(() => {
    ref.current = value;
  });

  return ref.current;
};
