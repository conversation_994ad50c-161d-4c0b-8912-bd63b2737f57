// Performance monitoring utilities for the Auften website

/**
 * Performance metrics tracking
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
  }

  /**
   * Start timing a performance metric
   * @param {string} name - Metric name
   */
  startTiming(name) {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null,
    });
  }

  /**
   * End timing a performance metric
   * @param {string} name - Metric name
   * @returns {number} Duration in milliseconds
   */
  endTiming(name) {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`);
      return 0;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    return metric.duration;
  }

  /**
   * Get timing for a metric
   * @param {string} name - Metric name
   * @returns {Object|null} Metric object or null
   */
  getTiming(name) {
    return this.metrics.get(name) || null;
  }

  /**
   * Get all metrics
   * @returns {Object} All metrics as an object
   */
  getAllMetrics() {
    const result = {};
    this.metrics.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics.clear();
  }

  /**
   * Log performance metrics to console
   */
  logMetrics() {
    console.group("Performance Metrics");
    this.metrics.forEach((metric, name) => {
      if (metric.duration !== null) {
        console.log(`${name}: ${metric.duration.toFixed(2)}ms`);
      }
    });
    console.groupEnd();
  }
}

/**
 * Web Vitals monitoring
 */
export const trackWebVitals = () => {
  // Check if browser supports the APIs
  if (!("PerformanceObserver" in window)) {
    console.warn("PerformanceObserver not supported");
    return;
  }

  // Track Largest Contentful Paint (LCP)
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const lastEntry = entries[entries.length - 1];
    console.log("LCP:", lastEntry.startTime.toFixed(2), "ms");
  });

  try {
    lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
  } catch (error) {
    console.warn("LCP observation failed:", error);
  }

  // Track First Input Delay (FID)
  const fidObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      const fid = entry.processingStart - entry.startTime;
      console.log("FID:", fid.toFixed(2), "ms");
    });
  });

  try {
    fidObserver.observe({ entryTypes: ["first-input"] });
  } catch (error) {
    console.warn("FID observation failed:", error);
  }

  // Track Cumulative Layout Shift (CLS)
  let clsValue = 0;
  const clsObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
      }
    });
    console.log("CLS:", clsValue.toFixed(4));
  });

  try {
    clsObserver.observe({ entryTypes: ["layout-shift"] });
  } catch (error) {
    console.warn("CLS observation failed:", error);
  }
};

/**
 * Resource loading performance
 */
export const trackResourceLoading = () => {
  if (!("PerformanceObserver" in window)) {
    return;
  }

  const resourceObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      if (
        entry.name.includes(".js") ||
        entry.name.includes(".css") ||
        entry.name.includes(".jpg") ||
        entry.name.includes(".png") ||
        entry.name.includes(".svg")
      ) {
        console.log(
          `Resource: ${entry.name.split("/").pop()} - ${entry.duration.toFixed(
            2
          )}ms`
        );
      }
    });
  });

  try {
    resourceObserver.observe({ entryTypes: ["resource"] });
  } catch (error) {
    console.warn("Resource observation failed:", error);
  }
};

/**
 * Bundle size analyzer
 */
export const analyzeBundleSize = () => {
  if (!navigator.connection) {
    console.warn("Network Information API not supported");
    return;
  }

  const connection = navigator.connection;
  const bundleMetrics = {
    effectiveType: connection.effectiveType,
    downlink: connection.downlink,
    rtt: connection.rtt,
    saveData: connection.saveData,
  };

  console.group("Bundle Analysis");
  console.log("Network conditions:", bundleMetrics);

  // Estimate bundle impact based on network
  if (
    connection.effectiveType === "slow-2g" ||
    connection.effectiveType === "2g"
  ) {
    console.warn("Slow network detected - consider optimizing bundle size");
  }

  if (connection.saveData) {
    console.warn("Data saver mode detected - consider reducing resource usage");
  }

  console.groupEnd();
};

/**
 * Memory usage monitoring
 */
export const trackMemoryUsage = () => {
  if (!("memory" in performance)) {
    console.warn("Memory API not supported");
    return;
  }

  const memory = performance.memory;
  const memoryInfo = {
    usedJSHeapSize: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + " MB",
    totalJSHeapSize: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + " MB",
    jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + " MB",
  };

  console.group("Memory Usage");
  console.table(memoryInfo);
  console.groupEnd();

  return memoryInfo;
};

/**
 * Component render performance
 */
export const trackComponentRender = (componentName) => {
  const startTime = performance.now();

  return () => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    console.log(`${componentName} render time: ${duration.toFixed(2)}ms`);
    return duration;
  };
};

/**
 * Image loading performance
 */
export const trackImageLoading = (imageUrl) => {
  const startTime = performance.now();

  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.log(
        `Image loaded: ${imageUrl.split("/").pop()} - ${duration.toFixed(2)}ms`
      );
      resolve({ duration, success: true });
    };

    img.onerror = () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.warn(
        `Image failed: ${imageUrl.split("/").pop()} - ${duration.toFixed(2)}ms`
      );
      reject({ duration, success: false });
    };

    img.src = imageUrl;
  });
};

/**
 * Scroll performance monitoring
 */
export const trackScrollPerformance = () => {
  let scrollCount = 0;
  let lastScrollTime = performance.now();

  const handleScroll = () => {
    scrollCount++;
    const now = performance.now();
    const timeSinceLastScroll = now - lastScrollTime;

    if (timeSinceLastScroll > 16.67) {
      // More than 60fps
      console.warn(
        `Scroll performance issue detected: ${timeSinceLastScroll.toFixed(
          2
        )}ms between frames`
      );
    }

    lastScrollTime = now;
  };

  window.addEventListener("scroll", handleScroll, { passive: true });

  return () => {
    window.removeEventListener("scroll", handleScroll);
    console.log(`Total scroll events: ${scrollCount}`);
  };
};

/**
 * Animation performance monitoring
 */
export const trackAnimationPerformance = (animationName) => {
  const startTime = performance.now();
  let frameCount = 0;
  let animationId;

  const trackFrame = () => {
    frameCount++;
    animationId = requestAnimationFrame(trackFrame);
  };

  trackFrame();

  return () => {
    cancelAnimationFrame(animationId);
    const endTime = performance.now();
    const duration = endTime - startTime;
    const fps = (frameCount / (duration / 1000)).toFixed(2);

    console.log(
      `Animation "${animationName}": ${fps} FPS over ${duration.toFixed(2)}ms`
    );
    return { fps: parseFloat(fps), duration, frameCount };
  };
};

// Create global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Development mode performance tracking
export const initPerformanceTracking = () => {
  if (process.env.NODE_ENV === "development") {
    console.log("🚀 Performance tracking initialized");

    // Track initial page load
    window.addEventListener("load", () => {
      setTimeout(() => {
        trackWebVitals();
        trackResourceLoading();
        analyzeBundleSize();
        trackMemoryUsage();
      }, 1000);
    });
  }
};

const performanceUtils = {
  PerformanceMonitor,
  performanceMonitor,
  trackWebVitals,
  trackResourceLoading,
  analyzeBundleSize,
  trackMemoryUsage,
  trackComponentRender,
  trackImageLoading,
  trackScrollPerformance,
  trackAnimationPerformance,
  initPerformanceTracking,
};

export default performanceUtils;
