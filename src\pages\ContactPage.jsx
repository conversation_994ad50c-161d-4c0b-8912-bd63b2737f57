import React, { useState, memo, useCallback } from "react";
import "./ContactPage.css";
import { isValidEmail, isValidPhone } from "../utils/helpers";

const ContactPage = memo(() => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Optimize change handler with useCallback
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    // Clear error when user starts typing
    setFormErrors((prevErrors) => {
      if (prevErrors[name]) {
        return {
          ...prevErrors,
          [name]: "",
        };
      }
      return prevErrors;
    });
  }, []);

  // Optimize validation function
  const validateForm = useCallback(() => {
    const errors = {};

    // Name validation
    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }

    // Email validation using helper function
    if (!formData.email) {
      errors.email = "Email is required";
    } else if (!isValidEmail(formData.email)) {
      errors.email = "Invalid email address";
    }

    // Phone validation using helper function
    if (formData.phone && !isValidPhone(formData.phone)) {
      errors.phone = "Invalid phone number";
    }

    // Message validation
    if (!formData.message.trim()) {
      errors.message = "Message is required";
    } else if (formData.message.length < 10) {
      errors.message = "Message must be at least 10 characters";
    }

    return errors;
  }, [formData.name, formData.email, formData.phone, formData.message]);

  // Optimize submit handler with useCallback
  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();

      // Validate form
      const errors = validateForm();
      setFormErrors(errors);

      // If no errors, submit form
      if (Object.keys(errors).length === 0) {
        setIsSubmitting(true);

        // Simulate API call
        setTimeout(() => {
          console.log("Form submitted:", formData);
          setIsSubmitting(false);
          setSubmitSuccess(true);

          // Reset form after successful submission
          setFormData({
            name: "",
            email: "",
            phone: "",
            message: "",
          });

          // Reset success message after 5 seconds
          setTimeout(() => {
            setSubmitSuccess(false);
          }, 5000);
        }, 1000);
      }
    },
    [validateForm, formData]
  );

  return (
    <main>
      <div className="o-wrapper u-pt-xl">
        <div className="contact-page" data-aos="fade-up">
          <h1 className="u-h1">Contact</h1>

          <div className="contact-grid">
            <div className="contact-info">
              <div className="office-info">
                <h2 className="u-h3">Melbourne Office</h2>
                <p>
                  Level 1, 165 Flinders Lane
                  <br />
                  Melbourne VIC 3000
                </p>
                <p>
                  <a href="tel:+61 3 9600 0222">+61 3 9600 0222</a>
                </p>
              </div>

              <div className="office-info">
                <h2 className="u-h3">Albury Office</h2>
                <p>
                  674 Olive Street
                  <br />
                  Albury NSW 2640
                </p>
                <p>
                  <a href="tel:+61 2 6021 5755">+61 2 6021 5755</a>
                </p>
              </div>

              <div className="contact-email">
                <h2 className="u-h3">Email</h2>
                <p>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
              </div>

              <div className="social-links">
                <h2 className="u-h3">Follow Us</h2>
                <div className="c-social">
                  <ul className="c-social__list">
                    <li className="c-social__list-item">
                      <a
                        href="https://www.instagram.com/auftenarchitects/"
                        target="_blank"
                        rel="noopener noreferrer"
                        title="View our Instagram page"
                      >
                        Instagram
                      </a>
                    </li>
                    <li className="c-social__list-item">
                      <a
                        href="https://au.linkedin.com/company/techn%C4%93-architecture-interior-design"
                        target="_blank"
                        rel="noopener noreferrer"
                        title="View our LinkedIn page"
                      >
                        LinkedIn
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="contact-form-container">
              <h2 className="u-h3">Get in touch</h2>

              {submitSuccess ? (
                <div className="form-success-message">
                  <p>Thank you for your message! We'll be in touch soon.</p>
                </div>
              ) : (
                <form className="contact-form" onSubmit={handleSubmit}>
                  <div
                    className={`form-group ${
                      formErrors.name ? "has-error" : ""
                    }`}
                  >
                    <label htmlFor="name">Name*</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                    />
                    {formErrors.name && (
                      <span className="error-message">{formErrors.name}</span>
                    )}
                  </div>

                  <div
                    className={`form-group ${
                      formErrors.email ? "has-error" : ""
                    }`}
                  >
                    <label htmlFor="email">Email*</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                    />
                    {formErrors.email && (
                      <span className="error-message">{formErrors.email}</span>
                    )}
                  </div>

                  <div
                    className={`form-group ${
                      formErrors.phone ? "has-error" : ""
                    }`}
                  >
                    <label htmlFor="phone">Phone</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                    />
                    {formErrors.phone && (
                      <span className="error-message">{formErrors.phone}</span>
                    )}
                  </div>

                  <div
                    className={`form-group ${
                      formErrors.message ? "has-error" : ""
                    }`}
                  >
                    <label htmlFor="message">Message*</label>
                    <textarea
                      id="message"
                      name="message"
                      rows="5"
                      value={formData.message}
                      onChange={handleChange}
                    ></textarea>
                    {formErrors.message && (
                      <span className="error-message">
                        {formErrors.message}
                      </span>
                    )}
                  </div>

                  <div className="form-footer">
                    <button
                      type="submit"
                      className="submit-button"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Sending..." : "Send message"}
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
});

ContactPage.displayName = "ContactPage";

export default ContactPage;
