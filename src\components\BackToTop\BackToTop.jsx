import React, { memo, useCallback } from "react";
import PropTypes from "prop-types";
import { useScrollPosition } from "../../hooks";
import { smoothScrollTo } from "../../utils/helpers";
import "./BackToTop.css";

const BackToTop = memo(({ threshold = 300, ariaLabel = "Back to top" }) => {
  const scrollY = useScrollPosition();
  const isVisible = scrollY > threshold;

  const handleScrollToTop = useCallback(() => {
    smoothScrollTo(0);
  }, []);

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        handleScrollToTop();
      }
    },
    [handleScrollToTop]
  );

  if (!isVisible) {
    return null;
  }

  return (
    <button
      className="back-to-top"
      onClick={handleScrollToTop}
      onKeyDown={handleKeyDown}
      aria-label={ariaLabel}
      type="button"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path
          d="M10 2.5L9.375 3.125L3.75 8.75L5 10L9.375 5.625V17.5H10.625V5.625L15 10L16.25 8.75L10.625 3.125L10 2.5Z"
          fill="currentColor"
        />
      </svg>
    </button>
  );
});

BackToTop.displayName = "BackToTop";

BackToTop.propTypes = {
  threshold: PropTypes.number,
  ariaLabel: PropTypes.string,
};

export default BackToTop;
