import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { useEffect, lazy, Suspense } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import "./App.css";
import { initWebVitals } from "./utils/webVitals";

// Import always-loaded components
import Header from "./components/Header/Header";
import Footer from "./components/Footer/Footer";
import ScrollToTop from "./components/ScrollToTop/ScrollToTop";
import ErrorBoundary from "./components/ErrorBoundary/ErrorBoundary";
import BackToTop from "./components/BackToTop/BackToTop";
import Spinner from "./components/Spinner/Spinner";

// Lazily load pages with prefetching for better performance
const HomePage = lazy(() =>
  import(/* webpackChunkName: "home" */ "./pages/HomePage")
);
const ProjectsPage = lazy(() =>
  import(/* webpackChunkName: "projects" */ "./pages/ProjectsPage")
);
const ProjectDetailPage = lazy(() =>
  import(/* webpackChunkName: "project-detail" */ "./pages/ProjectDetailPage")
);
const StudioPage = lazy(() =>
  import(/* webpackChunkName: "studio" */ "./pages/StudioPage")
);
const ContactPage = lazy(() =>
  import(/* webpackChunkName: "contact" */ "./pages/ContactPage")
);
const NotFoundPage = lazy(() =>
  import(/* webpackChunkName: "not-found" */ "./pages/NotFoundPage")
);

function App() {
  useEffect(() => {
    // Initialize AOS animation library
    AOS.init({
      duration: 800,
      once: true,
    });

    // Initialize Web Vitals monitoring
    initWebVitals();
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <ErrorBoundary>
        <div className="site" id="site">
          <Header />
          <Suspense
            fallback={
              <div className="page-loading">
                <Spinner />
              </div>
            }
          >
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/projects" element={<ProjectsPage />} />
              <Route path="/projects/:slug" element={<ProjectDetailPage />} />
              <Route path="/studio" element={<StudioPage />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
          <Footer />
          <BackToTop />
        </div>
      </ErrorBoundary>
    </Router>
  );
}

export default App;
