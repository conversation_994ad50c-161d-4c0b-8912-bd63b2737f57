import React, { memo } from "react";
import PropTypes from "prop-types";
import "./Spinner.css";

const Spinner = memo(
  ({
    size = "medium",
    color = "primary",
    label = "Loading...",
    showLabel = false,
    className = "",
  }) => {
    return (
      <div
        className={`spinner-container ${size} ${color} ${className}`}
        role="status"
        aria-label={label}
      >
        <div className="spinner" aria-hidden="true"></div>
        {showLabel && (
          <span className="spinner-label" aria-live="polite">
            {label}
          </span>
        )}
      </div>
    );
  }
);

Spinner.displayName = "Spinner";

Spinner.propTypes = {
  size: PropTypes.oneOf(["small", "medium", "large"]),
  color: PropTypes.oneOf(["primary", "secondary", "white", "dark"]),
  label: PropTypes.string,
  showLabel: PropTypes.bool,
  className: PropTypes.string,
};

export default Spinner;
