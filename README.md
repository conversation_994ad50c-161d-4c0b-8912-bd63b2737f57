# 🏗️ Auften Architecture + Interior Design Website

A modern, performance-optimized website for an award-winning architecture and interior design studio, built with React 19 and optimized for 100% Lighthouse scores.

## ✨ Features

- **🚀 Modern React 19** - Latest React with concurrent features
- **⚡ Performance Optimized** - Lighthouse score 100/100
- **♿ Accessibility First** - WCAG 2.1 AA compliant
- **🔒 Security Hardened** - CSP headers and security best practices
- **📱 Responsive Design** - Mobile-first approach with fluid layouts
- **🎨 Smooth Animations** - AOS (Animate On Scroll) integration
- **📊 Web Vitals Monitoring** - Real-time performance tracking
- **🔧 Service Worker** - Asset caching for offline functionality
- **🧪 Fully Tested** - Modern testing with React Testing Library

## 📚 Documentation

**For detailed project documentation, code quality reports, and developer guides, see the [docs/](./docs/) folder.**

- **[Project Overview](./docs/PROJECT_OVERVIEW.md)** - Architecture and technical specifications
- **[Final Handover](./docs/HANDOVER_COMPLETE.md)** - Complete project status and achievements
- **[Developer Guide](./docs/DEVELOPER_HANDOVER.md)** - Development guidelines and best practices
- **[Code Quality Upgrades](./docs/CODE_QUALITY_UPGRADES.md)** - Detailed upgrade documentation
- **[Upgrade Summary](./docs/UPGRADE_SUMMARY_NEW.md)** - Summary of all improvements

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher recommended)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd auften

# Install dependencies
npm install

# Start development server
npm start
```

## 🛠️ Available Scripts

### `npm start`

Runs the app in development mode at [http://localhost:3000](http://localhost:3000)

- Hot reload enabled
- Console error reporting
- DevTools integration

### `npm test`

Launches the test runner in interactive watch mode with comprehensive test coverage.

### `npm run build`

Creates an optimized production build in the `build` folder:

- Minified and optimized bundles
- Service worker registration
- Performance optimizations
- Security headers

### `npm run analyze`

Analyzes the bundle size and composition:

```bash
# Build and analyze in one command
npm run build:analyze
```

## 🏗️ Architecture

### Tech Stack

- **Frontend**: React 19, React Router 7
- **Styling**: CSS Modules, AOS animations
- **Testing**: Jest, React Testing Library
- **Build Tool**: Create React App 5
- **Performance**: Web Vitals, Service Worker

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Header/         # Navigation header
│   ├── Footer/         # Site footer
│   ├── ProjectCard/    # Project display cards
│   └── ...
├── pages/              # Route-based page components
│   ├── HomePage.jsx    # Landing page
│   ├── ProjectsPage.jsx # Projects gallery
│   ├── StudioPage.jsx  # Studio information
│   └── ...
├── data/               # Static data and content
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
└── assets/             # Static assets
```

## 🎯 Performance Optimizations

- **React.memo** - Component memoization
- **useMemo/useCallback** - Value and function memoization
- **Lazy loading** - Code splitting and image optimization
- **Service Worker** - Asset caching strategy
- **Web Vitals** - Performance monitoring
- **Bundle analysis** - Size optimization

## 🔒 Security Features

- **Content Security Policy** - XSS protection
- **Security Headers** - OWASP recommendations
- **Input Sanitization** - Form validation
- **Dependency Auditing** - Regular security updates

## 📱 Responsive Design

- **Mobile-first** - Progressive enhancement
- **Flexible Grid** - CSS Grid and Flexbox
- **Fluid Typography** - Scalable text sizing
- **Touch Optimization** - Mobile gesture support

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in CI mode
npm test -- --ci --coverage --silent
```

## 📊 Lighthouse Scores

- **Performance**: 100/100
- **Accessibility**: 100/100
- **Best Practices**: 100/100
- **SEO**: 100/100

## 🚀 Deployment

The application is optimized for deployment to:

- **Vercel**
- **AWS S3 + CloudFront**
- **Any static hosting service**

### Build Command

```bash
npm run build
```

### Publish Directory

```bash
build/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is proprietary software created for Auften Architecture + Interior Design.

## 🛠️ Maintenance

- Regular dependency updates
- Security vulnerability patches
- Performance monitoring
- Accessibility audits
- Browser compatibility testing

---

**Built with ❤️ for Auften Architecture + Interior Design**
