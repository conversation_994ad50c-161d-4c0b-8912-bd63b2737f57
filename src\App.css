/* Main Application Styles */
:root {
  --primary-color: #ff4338;
  --text-color: #000000;
  --background-light: #ffffff;
  --background-gray: #f5f5f5;
  --section-color: #000000;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Arial", sans-serif;
  color: var(--text-color);
  line-height: 1.6;
}

/* Utility Classes */
.u-light-bg {
  background-color: var(--background-light);
}

.u-pt-xl {
  padding-top: 5rem;
}

.u-h4 {
  font-size: 1.25rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.u-h3 {
  font-size: 1.5rem;
  line-height: 1.3;
  margin-bottom: 1.5rem;
}

.u-h1 {
  font-size: 2.5rem;
  line-height: 1.2;
  margin-bottom: 2rem;
}

.u-mb-l {
  margin-bottom: 3rem;
}

.u-mb-s {
  margin-bottom: 1rem;
}

.u-mb-m {
  margin-bottom: 2rem;
}

.u-text-white {
  color: white;
}

.u-align-right {
  text-align: right;
}

.u-fade-in {
  opacity: 0;
  animation: fadeIn 0.5s forwards;
}

.u-btn-reset {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Layout */
.o-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.o-layout {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1rem;
}

.o-layout__item {
  padding: 0 1rem;
  width: 100%;
}

.site {
  position: relative;
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Lazy loading page spinner */
.page-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

/* Media Queries */
@media (min-width: 768px) {
  .u-1\/2\@tablet {
    width: 50%;
  }

  .u-3\/12\@tablet {
    width: 25%;
  }

  .u-4\/12\@tablet {
    width: 33.333%;
  }

  .u-6\/12\@tablet {
    width: 50%;
  }

  .u-8\/12\@tablet {
    width: 66.667%;
  }

  .u-9\/12\@tablet {
    width: 75%;
  }
}

@media (min-width: 1024px) {
  .u-1\/2\@desktop {
    width: 50%;
  }
}
