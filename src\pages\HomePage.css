/* HomePage styles */
.c-intro {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #ff4338;
  margin-top: 0; /* No negative margin needed since body padding is removed */
  padding-top: 0;
}

.c-intro__motion {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.c-intro__motion video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Ensure other intro elements are above the video */
.c-intro__outro,
.c-intro__macron,
.c-intro__cue {
  position: relative;
  z-index: 2;
}

.c-intro__outro {
  position: absolute;
  top: 50%;
  left: 5%;
  transform: translateY(-50%);
  z-index: 2;
  opacity: 0;
  transition: opacity 1s ease;
  max-width: 600px;
}

.c-intro__outro.visible {
  opacity: 1;
}

/* Hero content container */
.c-intro__content {
  text-align: left;
}

/* Hero text styling */
.c-intro__hero-text {
  font-family: "Domine", serif;
  color: #ff073a;
  font-weight: 600;
  line-height: 1.2;
  text-align: left;
  margin-bottom: 2rem;
}

/* Description paragraph styling */
.c-intro__description {
  font-family: "Outfit", sans-serif;
  color: #000000;
  font-weight: 400;
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: left;
  margin: 0;
  max-width: 500px;
}

/* Story section styling */
.c-story-section {
  margin-top: 3rem;
  max-width: 100%;
  position: relative;
  padding: 2rem 0;
}

.c-story-header {
  position: relative;
  margin-bottom: 3rem;
  z-index: 2;
}

.c-story-ellipse {
  position: absolute;
  left: -8rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  opacity: 0.6;
}

.c-story-ellipse img {
  width: 20rem;
  height: auto;
  object-fit: contain;
}

.c-story-title {
  font-family: "Domine", serif;
  color: #000000;
  font-weight: 600;
  font-size: clamp(2rem, 4vw, 2.8rem);
  line-height: 1.1;
  text-align: left;
  margin: 0;
  position: relative;
  z-index: 3;
  max-width: 25rem;
}

.c-story-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 4rem;
  align-items: start;
  position: relative;
  z-index: 2;
}

.c-story-text {
  max-width: 32rem;
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
}

.c-story-paragraph {
  font-family: "Outfit", sans-serif;
  color: #333333;
  font-weight: 400;
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  line-height: 1.65;
  text-align: left;
  margin: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

.c-story-founder-image {
  width: 18rem;
  position: relative;
}

.c-story-founder-image img {
  width: 100%;
  height: auto;
  border-radius: 0.875rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.c-story-founder-image img:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2);
}

.c-intro__macron {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.c-intro__macron-play {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: fadeIn 1s ease forwards 1s;
}

.c-intro__loader {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

.c-intro__stage {
  position: relative;
  z-index: 2;
}

.c-intro__scene-one {
  animation: pulsate 3s infinite;
}

.c-intro__cue {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.875rem;
  opacity: 0;
  animation: fadeIn 1s ease forwards 2s, bounce 1.5s infinite 2.5s;
}

.c-intro__cue svg {
  fill: white;
}

/* Text underline animations */
em[data-svg="straight"] {
  position: relative;
  font-style: normal;
  z-index: 1;
}

em[data-svg="straight"]::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0;
  height: 2px;
  background-color: #ff4338;
  transition: width 0.5s ease;
  z-index: -1;
}

em[data-svg="straight"][data-svg-animated="true"]::after {
  width: 100%;
}

em[data-svg="scribble"] {
  position: relative;
  font-style: normal;
  z-index: 1;
  display: inline-block;
}

em[data-svg="scribble"]::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -4px;
  width: 100%;
  height: 10px;
  background-image: url("data:image/svg+xml,%3Csvg width='100%25' height='100%25' viewBox='0 0 100 10' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,5 Q10,3 20,5 T40,5 T60,5 T80,5 T100,5' stroke='%23FF4338' fill='none' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center bottom;
  background-size: 100% 10px;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

em[data-svg="scribble"][data-svg-animated="true"]::after {
  opacity: 1;
}

/* About Section - Minimal Award-Winning Design */
.about-section {
  margin: 8rem 0;
  padding: 0 2rem;
}

.about-header {
  margin-bottom: 4rem;
  text-align: center;
}

.about-title {
  font-size: 2.5rem;
  font-weight: 300;
  letter-spacing: -0.02em;
  margin: 0 0 0.5rem;
  color: #000;
}

.about-subtitle {
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  color: #ff4338;
  position: relative;
}

.about-subtitle::after {
  content: "";
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2rem;
  height: 1px;
  background: #ff4338;
}

.leadership-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5rem;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .leadership-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
  }
}

.leader-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.leader-image-container {
  margin-bottom: 2rem;
  position: relative;
}

.leader-image {
  width: 280px;
  height: 320px;
  position: relative;
  overflow: hidden;
  border-radius: 2px;
}

.leader-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
  transition: opacity 0.4s ease;
}

.leader-card:hover .leader-image::before {
  opacity: 0;
}

.leader-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: grayscale(20%);
}

.leader-card:hover .leader-image img {
  transform: scale(1.02);
  filter: grayscale(0%);
}

.leader-info {
  max-width: 300px;
}

.leader-name {
  font-size: 1.5rem;
  font-weight: 400;
  letter-spacing: -0.01em;
  margin: 0 0 0.25rem;
  color: #000;
}

.leader-role {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: #ff4338;
  margin: 0 0 1.5rem;
}

.leader-description {
  font-size: 0.9rem;
  line-height: 1.7;
  color: #666;
  margin: 0;
  font-weight: 300;
}

@media (max-width: 768px) {
  .about-section {
    margin: 6rem 0;
    padding: 0 1rem;
  }

  .about-title {
    font-size: 2rem;
  }

  .leadership-grid {
    gap: 4rem;
  }

  .leader-image {
    width: 240px;
    height: 280px;
  }
}

/* Featured Projects Section - Minimal Award-Winning Design */
.featured-projects-section {
  margin: 8rem 0;
  padding: 0 2rem;
}

.projects-header {
  margin-bottom: 4rem;
  text-align: center;
}

.projects-subtitle {
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  color: #ff4338;
  margin-bottom: 0.5rem;
  position: relative;
}

.projects-subtitle::after {
  content: "";
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2rem;
  height: 1px;
  background: #ff4338;
}

.projects-title {
  font-size: 2.5rem;
  font-weight: 300;
  letter-spacing: -0.02em;
  margin: 0;
  color: #000;
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto 4rem;
}

@media (min-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.project-item {
  position: relative;
}

.project-link {
  display: block;
  text-decoration: none;
  color: inherit;
  position: relative;
}

.project-image-container {
  position: relative;
  overflow: hidden;
  aspect-ratio: 4/5;
  background: #f8f8f8;
}

.project-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: grayscale(10%);
}

.project-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 3rem 1.5rem 1.5rem;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-item:hover .project-overlay {
  transform: translateY(0);
}

.project-item:hover .project-image-container img {
  transform: scale(1.02);
  filter: grayscale(0%);
}

.project-details {
  color: white;
}

.project-name {
  font-size: 1.25rem;
  font-weight: 400;
  letter-spacing: -0.01em;
  margin: 0 0 0.25rem;
}

.project-type {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  opacity: 0.8;
}

.projects-footer {
  text-align: center;
}

.view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  text-decoration: none;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.05em;
  border: 1px solid #e0e0e0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.view-all-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: #ff4338;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.view-all-btn:hover {
  color: white;
  border-color: #ff4338;
}

.view-all-btn:hover::before {
  left: 0;
}

.view-all-btn svg {
  transition: transform 0.3s ease;
}

.view-all-btn:hover svg {
  transform: translateX(0.25rem);
}

@media (max-width: 768px) {
  .featured-projects-section {
    margin: 6rem 0;
    padding: 0 1rem;
  }

  .projects-title {
    font-size: 2rem;
  }

  .projects-grid {
    gap: 2rem;
    margin-bottom: 3rem;
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes pulsate {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Project card styles */
.c-project-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 400px;
}

.c-project-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
  color: white;
}

.c-project-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.c-project-card__image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0.3) 58.94%,
    rgba(0, 0, 0, 0.5) 100%
  );
}

.c-project-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.c-project-card__action:hover .c-project-card__image {
  transform: scale(1.05);
}

.c-project-card__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 1.5rem;
  z-index: 1;
}

.c-project-card__meta {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}
/* CTA card styles */
.c-cta-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 500px;
}

.c-cta-card.red::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 67, 56, 0.5);
  z-index: 1;
}

.c-cta-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
}

.c-cta-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.c-cta-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.c-cta-card__action:hover .c-cta-card__image {
  transform: scale(1.05);
}

.c-cta-card__content {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 2rem;
  z-index: 2;
}

/* Responsive */
@media (max-width: 480px) {
  .c-intro__outro {
    left: 3%;
    max-width: 90%;
  }

  .c-intro__hero-text {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .c-intro__description {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .c-story-section {
    margin-top: 2rem;
    padding: 1.5rem 1rem;
  }

  .c-story-header {
    margin-bottom: 2rem;
  }

  .c-story-ellipse {
    left: -2rem;
    opacity: 0.4;
  }

  .c-story-ellipse img {
    width: 12rem;
  }

  .c-story-title {
    font-size: clamp(1.75rem, 6vw, 2.2rem);
    max-width: 20rem;
    line-height: 1.15;
  }

  .c-story-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    text-align: left;
  }

  .c-story-text {
    max-width: 100%;
    gap: 1.5rem;
    order: 1;
  }

  .c-story-paragraph {
    font-size: clamp(0.95rem, 3.5vw, 1.05rem);
    line-height: 1.6;
  }

  .c-story-founder-image {
    width: 100%;
    max-width: 16rem;
    justify-self: center;
    order: 2;
  }
}

@media (min-width: 481px) and (max-width: 767px) {
  .c-intro__outro {
    left: 4%;
    max-width: 85%;
  }

  .c-intro__hero-text {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .c-intro__description {
    font-size: 1rem;
    line-height: 1.5;
  }

  .c-story-section {
    padding: 2rem 2rem;
    margin-top: 2.5rem;
  }

  .c-story-header {
    margin-bottom: 2.5rem;
  }

  .c-story-ellipse {
    left: -3rem;
    opacity: 0.5;
  }

  .c-story-ellipse img {
    width: 16rem;
  }

  .c-story-title {
    font-size: clamp(2rem, 5vw, 2.5rem);
    max-width: 24rem;
  }

  .c-story-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .c-story-text {
    max-width: 100%;
    gap: 1.75rem;
  }

  .c-story-paragraph {
    font-size: clamp(1.05rem, 2.8vw, 1.15rem);
    line-height: 1.65;
  }

  .c-story-founder-image {
    width: 100%;
    max-width: 20rem;
    justify-self: center;
  }
}

@media (min-width: 768px) {
  .c-project-card {
    height: 500px;
  }

  .c-intro__outro {
    left: 5%;
    max-width: 600px;
  }

  /* Desktop story section adjustments */
  .c-story-section {
    padding: 3rem 0;
    margin-top: 4rem;
  }

  .c-story-header {
    margin-bottom: 3.5rem;
  }

  .c-story-ellipse {
    left: -10rem;
    opacity: 0.6;
  }

  .c-story-ellipse img {
    width: 24rem;
  }

  .c-story-title {
    font-size: clamp(2.5rem, 3.5vw, 3rem);
    max-width: 28rem;
    margin-left: -2rem;
  }

  .c-story-content {
    grid-template-columns: 1fr auto;
    gap: 5rem;
    align-items: start;
  }

  .c-story-text {
    max-width: 32rem;
    margin-left: -2rem;
    gap: 2rem;
  }

  .c-story-paragraph {
    font-size: clamp(1.125rem, 1.5vw, 1.25rem);
    line-height: 1.7;
  }

  .c-story-founder-image {
    width: 20rem;
    position: sticky;
    top: 2rem;
  }

  .c-story-paragraph {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    text-align: left;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
  }

  .c-intro__hero-text {
    font-size: 3.5rem;
  }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .c-story-section {
    padding: 4rem 0;
    margin-top: 5rem;
  }

  .c-story-ellipse {
    left: -12rem;
  }

  .c-story-ellipse img {
    width: 28rem;
  }

  .c-story-title {
    font-size: clamp(2.8rem, 3vw, 3.2rem);
    margin-left: -3rem;
  }

  .c-story-content {
    gap: 6rem;
  }

  .c-story-text {
    max-width: 36rem;
    margin-left: -3rem;
    gap: 2.25rem;
  }

  .c-story-paragraph {
    font-size: clamp(1.2rem, 1.3vw, 1.3rem);
    line-height: 1.75;
  }

  .c-story-founder-image {
    width: 22rem;
    margin-bottom: 2rem;
  }

  .c-intro__description {
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 500px;
  }
}

@media (min-width: 1024px) {
  .c-intro__hero-text {
    font-size: 4rem;
  }

  .c-intro__description {
    font-size: 1.2rem;
    max-width: 550px;
  }
}

@media (min-width: 1440px) {
  .c-intro__hero-text {
    font-size: 4.5rem;
  }

  .c-intro__description {
    font-size: 1.3rem;
    max-width: 600px;
  }
}
