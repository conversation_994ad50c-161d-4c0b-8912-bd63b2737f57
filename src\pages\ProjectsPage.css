/* Projects Page styles */
.projects-page {
  padding-bottom: 4rem;
}

.projects-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin: 2rem 0;
}

.filter-button {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.filter-button:hover {
  border-color: #999;
}

.filter-button.active {
  background-color: #FF4338;
  color: white;
  border-color: #FF4338;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.featured-projects {
  margin-bottom: 3rem;
}

.featured-banner {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 1rem;
  margin-bottom: 2rem;
  border-radius: 4px;
}

.featured-banner__icon {
  margin-right: 1rem;
  color: #FF4338;
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}
