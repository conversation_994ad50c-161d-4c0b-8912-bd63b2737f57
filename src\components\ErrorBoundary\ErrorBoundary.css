/* ErrorBoundary styles */
.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f8f8f8;
}

.error-container {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 90%;
  text-align: center;
}

.error-container h2 {
  color: #ff4338;
  margin-bottom: 1rem;
}

.error-container button {
  background-color: #ff4338;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  margin-top: 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.error-container button:hover {
  background-color: #e63c31;
}

.error-details {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  text-align: left;
}

.error-details h3 {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.stack-trace {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  font-size: 0.9rem;
  margin-top: 1rem;
  white-space: pre-wrap;
  color: #666;
}
