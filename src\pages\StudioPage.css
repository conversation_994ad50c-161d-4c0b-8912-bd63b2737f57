/* Studio Page styles */
.studio-page {
  padding-bottom: 4rem;
}

.studio-intro {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  margin-bottom: 4rem;
}

.studio-intro__content {
  flex: 1;
  min-width: 300px;
}

.studio-intro__image {
  flex: 1;
  min-width: 300px;
  height: 400px;
  border-radius: 4px;
  overflow: hidden;
}

.studio-intro__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Text underline animations */
em[data-svg="straight"] {
  position: relative;
  font-style: normal;
  z-index: 1;
}

em[data-svg="straight"]::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0;
  height: 2px;
  background-color: #ff4338;
  transition: width 0.5s ease;
  z-index: -1;
}

em[data-svg="straight"][data-svg-animated="true"]::after {
  width: 100%;
}

em[data-svg="scribble"] {
  position: relative;
  font-style: normal;
  z-index: 1;
}

em[data-svg="scribble"]::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -3px;
  width: 0;
  height: 6px;
  background: url("data:image/svg+xml,%3Csvg width='100' height='6' viewBox='0 0 100 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 3C8 1 11 5 19 3C27 1 30 5 38 3C46 1 49 5 57 3C65 1 68 5 76 3C84 1 87 5 95 3C97 1 99 3 100 3' stroke='%23FF4338' stroke-width='2'/%3E%3C/svg%3E");
  background-repeat: repeat-x;
  background-size: contain;
  transition: width 1s ease;
  z-index: -1;
  opacity: 0.9;
}

em[data-svg="scribble"][data-svg-animated="true"]::after {
  width: 100%;
}

.studio-values {
  background-color: #f5f5f5;
  padding: 4rem 0;
  margin: 4rem 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.value-item {
  padding: 1.5rem;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.value-item__number {
  font-size: 3rem;
  font-weight: 700;
  color: #ff4338;
  margin-bottom: 1rem;
}

/* Team section */
.team-section {
  margin-bottom: 4rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.team-member {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.team-member__image {
  height: 300px;
  overflow: hidden;
}

.team-member__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-member__content {
  padding: 1.5rem;
}

.team-member__name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.team-member__role {
  color: #666;
  margin-bottom: 1rem;
}

.team-member__bio {
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Careers section */
.careers-section {
  background-color: #ff4338;
  color: white;
  padding: 4rem 0;
  margin-top: 4rem;
}

.careers-content {
  max-width: 600px;
}

.careers-btn {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: #ff4338;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.careers-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}
