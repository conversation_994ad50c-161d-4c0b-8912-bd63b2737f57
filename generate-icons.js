// Generate favicon and app icons
const fs = require("fs");
const path = require("path");
const sharp = require("sharp");

const PUBLIC_DIR = path.join(__dirname, "public");
const SVG_PATH = path.join(PUBLIC_DIR, "favicon.svg");
const FAVICON_PATH = path.join(PUBLIC_DIR, "favicon.ico");
const PRIMARY_COLOR = "#FF4338"; // Auften's red color

// Favicon sizes
const FAVICON_SIZES = [16, 24, 32, 48, 64];

// App icon sizes
const APP_ICON_SIZES = [192, 512];

// Create a square canvas with the letter T for different sizes
const generateIcon = async (size, outputPath) => {
  try {
    await sharp(SVG_PATH).resize(size, size).toFile(outputPath);
    console.log(`Generated: ${path.basename(outputPath)}`);
  } catch (err) {
    console.error(`Error generating ${outputPath}:`, err);
  }
};

// Main function to generate all icons
const generateIcons = async () => {
  try {
    // Generate app icons
    for (const size of APP_ICON_SIZES) {
      await generateIcon(size, path.join(PUBLIC_DIR, `logo${size}.png`));
    }

    // Generate Apple touch icon
    await generateIcon(180, path.join(PUBLIC_DIR, "apple-touch-icon.png"));

    console.log("All icons generated successfully");
  } catch (err) {
    console.error("Error generating icons:", err);
  }
};

// Run the icon generator
generateIcons();
