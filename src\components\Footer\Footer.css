/* Footer styles */
.c-footer {
  background-color: #ffffff;
  padding: 4rem 0 2rem;
  margin-top: 4rem;
}

.o-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.c-footer__body {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 4rem;
}

.c-footer__body-col {
  flex: 1;
  min-width: 250px;
}

.c-footer__content {
  margin-bottom: 2rem;
}

.c-logo--footer {
  margin-bottom: 1.5rem;
}

.c-logo--footer img {
  height: 56px;
  width: auto;
  max-width: 72px;
}

.c-footer__sign-up {
  flex: 1;
  min-width: 300px;
}

.u-mb-s {
  margin-bottom: 1rem;
}

.u-mb-m {
  margin-bottom: 2rem;
}

.u-h4 {
  font-size: 1.25rem;
  font-weight: 500;
  color: #000;
  text-decoration: none;
  transition: color 0.3s ease;
}

.u-h4:hover,
.c-footer__content a:hover {
  color: #ff4338;
}

.c-footer__content a {
  text-decoration: none;
  color: #000;
  transition: color 0.3s ease;
}

.c-social__list {
  display: flex;
  padding: 0;
  list-style: none;
  gap: 1rem;
}

.c-social__list-item a {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Newsletter form */
.newsletter-form {
  background-color: #f5f5f5;
  padding: 2rem;
  border-radius: 4px;
}

.u-h5 {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.medium {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
}

.gform_button {
  background-color: #ff4338;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.gform_button:hover {
  background-color: #e63c31;
}

/* Footer info */
.c-footer__info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.c-footer__info-nav {
  display: flex;
  list-style: none;
  padding: 0;
  gap: 2rem;
}

.c-footer__info-nav li a {
  text-decoration: none;
  color: #666;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.c-footer__info-nav li a:hover {
  color: #ff4338;
}

.c-footer__webforge a {
  opacity: 0.7;
  transition: opacity 0.3s ease;
  text-decoration: none;
}

.c-footer__webforge a:hover {
  opacity: 1;
}

.webforge-w {
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Helvetica Neue", Arial, sans-serif;
  font-weight: 900;
  font-size: 1.5rem;
  color: #ff4338;
  text-shadow: 0 2px 4px rgba(255, 67, 56, 0.3);
  background: linear-gradient(135deg, #ff4338 0%, #e63c31 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
  display: inline-block;
}

.c-footer__webforge a:hover .webforge-w {
  transform: scale(1.1);
  text-shadow: 0 4px 8px rgba(255, 67, 56, 0.4);
}

@media (max-width: 768px) {
  .c-footer__body {
    flex-direction: column;
  }

  .c-footer__info {
    flex-direction: column;
    gap: 1.5rem;
  }
}
