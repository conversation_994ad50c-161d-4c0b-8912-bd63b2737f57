.project-detail-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 67, 56, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.project-hero {
  width: 100%;
  height: 60vh;
  overflow: hidden;
  position: relative;
}

.project-hero__image {
  width: 100%;
  height: 100%;
}

.project-hero__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.project-info {
  padding: 3rem 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .project-info {
    grid-template-columns: 1fr 2fr;
  }
}

.project-info__header {
  margin-bottom: 1.5rem;
}

.project-info__meta {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.project-info__meta p {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.project-info__title {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.project-info__description {
  font-size: 1.1rem;
  line-height: 1.6;
}

.project-full-description {
  margin-top: 2rem;
}

.project-full-description p {
  margin-bottom: 1.5rem;
}

.project-gallery {
  margin: 3rem 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .gallery-item--large {
    grid-column: span 2;
  }
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.related-projects {
  margin: 4rem 0;
}

.related-title {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  position: relative;
  display: inline-block;
}

.related-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.related-projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .related-projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.related-project-card {
  transition: transform 0.3s ease;
}

.related-project-card:hover {
  transform: translateY(-5px);
}

.related-project-link {
  text-decoration: none;
  color: var(--text-primary);
}

.related-project-image {
  margin-bottom: 1rem;
  overflow: hidden;
}

.related-project-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.related-project-link:hover .related-project-image img {
  transform: scale(1.05);
}

.related-project-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.related-project-meta {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.project-navigation {
  padding: 2rem 0;
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.back-to-projects {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.back-to-projects:hover {
  color: #c42828;
}

.back-arrow {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}
