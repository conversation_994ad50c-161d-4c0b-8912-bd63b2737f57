import React, { useEffect, memo, useMemo } from "react";
import { Link } from "react-router-dom";
import "./StudioPage.css";
import LazyImage from "../components/LazyImage/LazyImage";
import teamMembers from "../data/teamMembers";
import CTACard from "../components/CTACard/CTACard";
import { useIntersectionObserver } from "../hooks/index";

const StudioPage = memo(() => {
  // Memoize intersection observer options to prevent recreation
  const intersectionOptions = useMemo(
    () => ({
      threshold: 0.3,
    }),
    []
  );

  // Use custom hook instead of manual intersection observer
  const [introTextRef, isTextVisible] =
    useIntersectionObserver(intersectionOptions);

  // Animate text underlines when visible
  useEffect(() => {
    if (isTextVisible) {
      const currentRef = introTextRef.current;
      if (currentRef) {
        const underlineElements = currentRef.querySelectorAll("em[data-svg]");
        underlineElements.forEach((el) => {
          setTimeout(() => {
            el.setAttribute("data-svg-animated", "true");
          }, 500);
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTextVisible]);
  return (
    <main>
      <div className="o-wrapper u-pt-xl">
        <div className="studio-page">
          <h1 className="u-h1" data-aos="fade-up">
            Studio
          </h1>

          {/* Studio Introduction */}
          <div className="studio-intro" data-aos="fade-up">
            <div className="studio-intro__content" ref={introTextRef}>
              <p className="c-intro-text">
                Auften -
                <em data-svg="scribble">'designing spaces that inspire'</em>
                defines our design process to create bespoke places and spaces
                that enrich the human experience.
              </p>
              <p>
                Founded in 2002, Auften Architecture + Interior Design has
                established itself as a leading Australian design practice. With
                studios in Melbourne and Albury, we bring design excellence and
                innovation to projects across residential, commercial,
                hospitality, and cultural sectors.
              </p>
              <p>
                Our approach is rooted in a deep understanding of both
                architectural and interior design, allowing us to create
                holistic spaces that are both beautiful and functional. We
                believe in the power of design to transform lives and
                communities.
              </p>
            </div>
            <div className="studio-intro__image">
              <LazyImage
                src="https://auften.com.au/content/uploads/2023/01/Studio-Team.jpg"
                alt="Auften Studio Team"
              />
            </div>
          </div>

          {/* Values Section */}
          <div className="studio-values">
            <div className="o-wrapper">
              <h2 className="u-h2" data-aos="fade-up">
                Our Values
              </h2>
              <div className="values-grid">
                <div className="value-item" data-aos="fade-up">
                  <div className="value-item__number">01</div>
                  <h3>Design Excellence</h3>
                  <p>
                    We strive for excellence in every project, pushing
                    boundaries and challenging conventions to create spaces that
                    inspire and endure.
                  </p>
                </div>
                <div
                  className="value-item"
                  data-aos="fade-up"
                  data-aos-delay="100"
                >
                  <div className="value-item__number">02</div>
                  <h3>Sustainability</h3>
                  <p>
                    Our commitment to environmental sustainability guides our
                    design decisions, from material selection to energy
                    efficiency.
                  </p>
                </div>
                <div
                  className="value-item"
                  data-aos="fade-up"
                  data-aos-delay="200"
                >
                  <div className="value-item__number">03</div>
                  <h3>Collaboration</h3>
                  <p>
                    We believe in the power of collaboration—with clients,
                    consultants, and communities—to achieve truly meaningful
                    outcomes.
                  </p>
                </div>
                <div
                  className="value-item"
                  data-aos="fade-up"
                  data-aos-delay="300"
                >
                  <div className="value-item__number">04</div>
                  <h3>Context & Culture</h3>
                  <p>
                    Our designs respond thoughtfully to their physical,
                    cultural, and historical context, creating spaces that
                    belong to their place.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Team Section */}
          <div className="team-section">
            <h2 className="u-h2" data-aos="fade-up">
              Our Team
            </h2>
            <div className="team-grid">
              {teamMembers.map((member) => (
                <div key={member.id} className="team-member" data-aos="fade-up">
                  <div className="team-member__image">
                    <LazyImage src={member.imageUrl} alt={member.name} />
                  </div>
                  <div className="team-member__content">
                    <h3 className="team-member__name">{member.name}</h3>
                    <p className="team-member__role">{member.role}</p>
                    <p className="team-member__bio">{member.bio}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Careers Section */}
          <div className="careers-section">
            <div className="o-wrapper">
              <div className="careers-content" data-aos="fade-up">
                <h2 className="u-h2">Join Our Team</h2>
                <p>
                  We're always looking for talented individuals who share our
                  passion for design excellence and innovation. If you're
                  interested in joining the Auften team, we'd love to hear from
                  you.
                </p>
                <Link to="/contact" className="careers-btn">
                  Get in Touch
                </Link>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="o-wrapper">
            <div className="o-layout o-layout--xlarge c-cta-card__wrap">
              <div className="o-layout__item u-1/2@tablet" data-aos="fade-up">
                <CTACard
                  title="Projects"
                  link="/projects"
                  imageUrl="https://images.unsplash.com/photo-1545958339-7ce3eea06a78?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80"
                />
              </div>
              <div
                className="o-layout__item u-1/2@tablet"
                data-aos="fade-up"
                data-aos-delay="200"
              >
                <CTACard
                  title="Projects"
                  link="/projects"
                  imageUrl="https://images.unsplash.com/photo-1545958339-7ce3eea06a78?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80"
                  color="red"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
});

StudioPage.displayName = "StudioPage";

export default StudioPage;
