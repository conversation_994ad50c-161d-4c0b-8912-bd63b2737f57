import React, { memo } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import LazyImage from "../LazyImage/LazyImage";
import "./ProjectCard.css";

const ProjectCard = memo(({ project }) => {
  const { id, title, slug, imageUrl, category, featured = false } = project;

  return (
    <article
      id={`projectCard${id}`}
      className={`c-project-card ${featured ? "featured" : ""}`}
    >
      <Link
        className="c-project-card__action"
        title={`View ${title} project details`}
        to={`/projects/${slug}`}
        aria-label={`View details for ${title}, a ${category || "project"}`}
      >
        <div className="c-project-card__image">
          <LazyImage src={imageUrl} alt={`${title} project preview`} />
        </div>
        <div className="c-project-card__content">
          <p className="c-project-card__meta" aria-label="Project category">
            &bull; {category || "Project"}
          </p>
          <h2 className="c-project-card__heading u-h3">{title}</h2>
        </div>
      </Link>
    </article>
  );
});

ProjectCard.displayName = "ProjectCard";

ProjectCard.propTypes = {
  project: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    title: PropTypes.string.isRequired,
    slug: PropTypes.string.isRequired,
    imageUrl: PropTypes.string.isRequired,
    category: PropTypes.string,
    featured: PropTypes.bool,
  }).isRequired,
};

export default ProjectCard;
