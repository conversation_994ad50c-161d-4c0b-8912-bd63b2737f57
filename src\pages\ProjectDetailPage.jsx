import React, { useEffect, useState, memo, useMemo, useCallback } from "react";
import { usePara<PERSON>, Link, useNavigate } from "react-router-dom";
import projects from "../data/projects";
import LazyImage from "../components/LazyImage/LazyImage";
import "./ProjectDetailPage.css";

const ProjectDetailPage = memo(() => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [relatedProjects, setRelatedProjects] = useState([]);

  // Memoize project finding to prevent unnecessary re-computation
  const foundProject = useMemo(
    () => projects.find((p) => p.slug === slug),
    [slug]
  );

  // Memoize related projects calculation
  const getRelatedProjects = useCallback((currentProject) => {
    return projects
      .filter(
        (p) =>
          p.category === currentProject.category && p.id !== currentProject.id
      )
      .slice(0, 3); // Get up to 3 related projects
  }, []);

  useEffect(() => {
    // Initialize AOS
    window.AOS.refresh();

    if (foundProject) {
      setProject(foundProject);

      // Find related projects using memoized function
      const related = getRelatedProjects(foundProject);
      setRelatedProjects(related);
      document.title = `${foundProject.title} | Auften Architecture + Interior Design`;
    } else {
      // Project not found, redirect to 404
      navigate("/not-found");
    }

    setLoading(false);
  }, [foundProject, navigate, getRelatedProjects]);

  if (loading) {
    return (
      <div className="project-detail-loading">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (!project) {
    return null; // This will never render as we redirect to 404
  }

  return (
    <main className="project-detail-page">
      <div className="project-hero" data-aos="fade-up">
        <div className="project-hero__image">
          <LazyImage src={project.imageUrl} alt={project.title} />
        </div>
      </div>

      <div className="o-wrapper">
        <div className="project-info" data-aos="fade-up">
          <div className="project-info__header">
            <div className="project-info__meta">
              <p>
                <span className="project-category">{project.category}</span>
                <span className="project-location">{project.location}</span>
                <span className="project-year">{project.year}</span>
              </p>
            </div>
            <h1 className="project-info__title">{project.title}</h1>
          </div>

          <div
            className="project-info__description"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <p>{project.description}</p>

            {/* This would be replaced with actual project content from the API */}
            <div className="project-full-description">
              <p>
                The {project.title} project exemplifies our studio's commitment
                to thoughtful design that responds to both context and client
                needs. Located in {project.location}, this
                {project.category.toLowerCase()} project was completed in{" "}
                {project.year}.
              </p>
              <p>
                Our approach focused on creating spaces that blend functionality
                with aesthetic excellence, ensuring that each element serves a
                purpose while contributing to the overall design language.
              </p>
            </div>
          </div>
        </div>

        {/* Project Gallery - Would be populated from API with actual images */}
        <div className="project-gallery" data-aos="fade-up">
          <div className="gallery-grid">
            <div className="gallery-item gallery-item--large">
              <LazyImage
                src={project.imageUrl}
                alt={`${project.title} - Main View`}
              />
            </div>
            <div className="gallery-item">
              <LazyImage
                src={project.imageUrl.replace(".jpg", "-2.jpg")}
                alt={`${project.title} - Detail`}
              />
            </div>
            <div className="gallery-item">
              <LazyImage
                src={project.imageUrl.replace(".jpg", "-3.jpg")}
                alt={`${project.title} - Interior`}
              />
            </div>
          </div>
        </div>

        {relatedProjects.length > 0 && (
          <div className="related-projects" data-aos="fade-up">
            <h2 className="related-title">Related Projects</h2>
            <div className="related-projects-grid">
              {relatedProjects.map((relatedProject) => (
                <div key={relatedProject.id} className="related-project-card">
                  <Link
                    to={`/projects/${relatedProject.slug}`}
                    className="related-project-link"
                  >
                    <div className="related-project-image">
                      <LazyImage
                        src={relatedProject.imageUrl}
                        alt={relatedProject.title}
                      />
                    </div>
                    <div className="related-project-info">
                      <h3 className="related-project-title">
                        {relatedProject.title}
                      </h3>
                      <p className="related-project-meta">
                        {relatedProject.category}
                      </p>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="project-navigation" data-aos="fade-up">
          <Link to="/projects" className="back-to-projects">
            <span className="back-arrow">←</span> Back to All Projects
          </Link>
        </div>
      </div>
    </main>
  );
});

ProjectDetailPage.displayName = "ProjectDetailPage";

export default ProjectDetailPage;
