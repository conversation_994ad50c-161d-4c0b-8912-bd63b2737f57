import React, { memo } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import LazyImage from "../LazyImage/LazyImage";
import "./CTACard.css";

const CTACard = memo(({ title, link, imageUrl, color = "" }) => {
  return (
    <article className={`c-cta-card ${color}`}>
      <Link
        className="c-cta-card__action"
        title={`View ${title}`}
        to={link}
        aria-label={`Navigate to ${title} section`}
      >
        <div className="c-cta-card__image">
          <LazyImage src={imageUrl} alt={`${title} background`} />
        </div>
        <div className="c-cta-card__content">
          <h2 className="c-cta-card__heading u-h1 u-text-white">{title}</h2>
        </div>
      </Link>
    </article>
  );
});

CTACard.displayName = "CTACard";

CTACard.propTypes = {
  title: PropTypes.string.isRequired,
  link: PropTypes.string.isRequired,
  imageUrl: PropTypes.string.isRequired,
  color: PropTypes.string,
};

export default CTACard;
