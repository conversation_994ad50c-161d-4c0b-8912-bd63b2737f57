// Focus management utilities for accessibility
import { useEffect, useRef, useState, useCallback } from "react";

/**
 * Focus Management Hook
 * Manages focus for accessibility, particularly useful for modals, navigation, and dynamic content
 */
export const useFocusManagement = (isActive = true) => {
  const previousFocusRef = useRef(null);
  const containerRef = useRef(null);

  // Focusable elements selector
  const FOCUSABLE_SELECTORS = [
    "a[href]",
    "button:not([disabled])",
    "input:not([disabled])",
    "select:not([disabled])",
    "textarea:not([disabled])",
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ].join(", ");

  /**
   * Get all focusable elements within container
   */
  const getFocusableElements = useCallback(() => {
    if (!containerRef.current) return [];

    const elements = containerRef.current.querySelectorAll(FOCUSABLE_SELECTORS);
    return Array.from(elements).filter((element) => {
      return (
        !element.hasAttribute("disabled") &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0
      );
    });
  }, [FOCUSABLE_SELECTORS]);

  /**
   * Store current focus before activating focus management
   */
  const storeFocus = useCallback(() => {
    if (document.activeElement && document.activeElement !== document.body) {
      previousFocusRef.current = document.activeElement;
    }
  }, []);

  /**
   * Restore focus to previously focused element
   */
  const restoreFocus = useCallback(() => {
    if (previousFocusRef.current && previousFocusRef.current.focus) {
      previousFocusRef.current.focus();
      previousFocusRef.current = null;
    }
  }, []);

  /**
   * Trap focus within container
   */
  const trapFocus = useCallback(
    (event) => {
      const focusableElements = getFocusableElements();
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (event.key === "Tab") {
        if (event.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement?.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement?.focus();
          }
        }
      }

      // Escape key to close
      if (event.key === "Escape" && typeof window !== "undefined") {
        event.preventDefault();
        restoreFocus();
      }
    },
    [getFocusableElements, restoreFocus]
  );

  /**
   * Focus first focusable element in container
   */
  const focusFirst = useCallback(() => {
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }, [getFocusableElements]);

  /**
   * Focus last focusable element in container
   */
  const focusLast = useCallback(() => {
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus();
    }
  }, [getFocusableElements]);

  // Set up focus trap when active
  useEffect(() => {
    if (isActive && containerRef.current) {
      storeFocus();
      document.addEventListener("keydown", trapFocus);

      // Focus first element after a brief delay
      const timeoutId = setTimeout(() => {
        focusFirst();
      }, 100);

      return () => {
        document.removeEventListener("keydown", trapFocus);
        clearTimeout(timeoutId);
        if (isActive) {
          restoreFocus();
        }
      };
    }
  }, [isActive, storeFocus, trapFocus, focusFirst, restoreFocus]);

  return {
    containerRef,
    focusFirst,
    focusLast,
    restoreFocus,
    getFocusableElements,
  };
};

/**
 * Skip Link Component for Screen Readers
 */
export const SkipLink = ({
  href = "#main-content",
  children = "Skip to main content",
}) => {
  return (
    <a
      href={href}
      className="skip-link"
      style={{
        position: "absolute",
        left: "-9999px",
        zIndex: 9999,
        padding: "8px 16px",
        background: "#000",
        color: "#fff",
        textDecoration: "none",
        fontSize: "14px",
        fontWeight: "bold",
        border: "2px solid #fff",
        borderRadius: "4px",
      }}
      onFocus={(e) => {
        e.target.style.left = "8px";
        e.target.style.top = "8px";
      }}
      onBlur={(e) => {
        e.target.style.left = "-9999px";
        e.target.style.top = "auto";
      }}
    >
      {children}
    </a>
  );
};

/**
 * Visually Hidden Component for Screen Reader Only Content
 */
export const VisuallyHidden = ({
  children,
  as: Component = "span",
  ...props
}) => {
  const visuallyHiddenStyles = {
    position: "absolute",
    width: "1px",
    height: "1px",
    padding: "0",
    margin: "-1px",
    overflow: "hidden",
    clip: "rect(0, 0, 0, 0)",
    whiteSpace: "nowrap",
    border: "0",
  };

  return (
    <Component style={visuallyHiddenStyles} {...props}>
      {children}
    </Component>
  );
};

/**
 * Focus Outline Management
 * Ensures focus outlines are only shown when navigating with keyboard
 */
export const useFocusOutline = () => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Tab") {
        document.body.classList.add("keyboard-user");
      }
    };

    const handleMouseDown = () => {
      document.body.classList.remove("keyboard-user");
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("mousedown", handleMouseDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mousedown", handleMouseDown);
    };
  }, []);
};

/**
 * Announce to Screen Readers
 * Dynamically announce content changes to screen readers
 */
export const announceToScreenReader = (message, priority = "polite") => {
  const announcement = document.createElement("div");
  announcement.setAttribute("aria-live", priority);
  announcement.setAttribute("aria-atomic", "true");
  announcement.setAttribute("class", "sr-announcement");
  announcement.style.cssText = `
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
  `;

  document.body.appendChild(announcement);

  // Add message after element is in DOM
  setTimeout(() => {
    announcement.textContent = message;

    // Remove after announcement
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }, 100);
};

/**
 * ARIA Live Region Hook
 * Manages live regions for dynamic content announcements
 */
export const useAriaLiveRegion = (priority = "polite") => {
  const liveRegionRef = useRef(null);

  useEffect(() => {
    // Create live region if it doesn't exist
    if (!liveRegionRef.current) {
      const liveRegion = document.createElement("div");
      liveRegion.setAttribute("aria-live", priority);
      liveRegion.setAttribute("aria-atomic", "true");
      liveRegion.setAttribute("class", "aria-live-region");
      liveRegion.style.cssText = `
        position: absolute;
        left: -9999px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;

      document.body.appendChild(liveRegion);
      liveRegionRef.current = liveRegion;
    }

    return () => {
      if (
        liveRegionRef.current &&
        document.body.contains(liveRegionRef.current)
      ) {
        document.body.removeChild(liveRegionRef.current);
      }
    };
  }, [priority]);

  const announce = (message) => {
    if (liveRegionRef.current) {
      liveRegionRef.current.textContent = message;
    }
  };

  return announce;
};

/**
 * Reduced Motion Detection
 * Detects user's motion preferences for accessibility
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addListener(handleChange);
    return () => mediaQuery.removeListener(handleChange);
  }, []);

  return prefersReducedMotion;
};

const accessibilityUtils = {
  useFocusManagement,
  SkipLink,
  VisuallyHidden,
  useFocusOutline,
  announceToScreenReader,
  useAriaLiveRegion,
  useReducedMotion,
};

export default accessibilityUtils;
