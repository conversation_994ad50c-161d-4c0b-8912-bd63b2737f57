import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import "./LazyImage.css";
import Spinner from "../Spinner/Spinner";
import { useIntersectionObserver } from "../../hooks";

const LazyImage = ({
  src,
  alt = "",
  className = "",
  placeholder = null,
  fallback = null,
  ...props
}) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);

  // Use intersection observer to only load when visible
  const [imageRef, isVisible] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: "50px",
  });

  // Start loading when image becomes visible
  useEffect(() => {
    if (isVisible && !shouldLoad && src) {
      setShouldLoad(true);
    }
  }, [isVisible, shouldLoad, src]);

  // Load image when shouldLoad is true
  useEffect(() => {
    if (!shouldLoad || !src) return;

    const img = new Image();
    img.src = src;

    img.onload = () => {
      setLoaded(true);
      setError(false);
    };

    img.onerror = () => {
      setError(true);
      setLoaded(true);
    };

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [shouldLoad, src]);

  const renderPlaceholder = () => {
    if (placeholder) {
      return placeholder;
    }

    return (
      <div className="lazy-image-placeholder">
        <Spinner size="small" />
      </div>
    );
  };

  const renderFallback = () => {
    if (fallback) {
      return fallback;
    }

    return (
      <div className="lazy-image-error">
        <span>Image not available</span>
      </div>
    );
  };

  return (
    <div
      ref={imageRef}
      className={`lazy-image-container ${className}`}
      {...props}
    >
      {!loaded && !error && renderPlaceholder()}

      {loaded && !error && (
        <img src={src} alt={alt} className="lazy-image loaded" loading="lazy" />
      )}

      {error && renderFallback()}
    </div>
  );
};

LazyImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string,
  className: PropTypes.string,
  placeholder: PropTypes.node,
  fallback: PropTypes.node,
};

export default LazyImage;
