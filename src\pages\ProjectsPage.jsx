import React, { useState, useEffect, memo, useMemo, useCallback } from "react";
import "./ProjectsPage.css";
import ProjectCard from "../components/ProjectCard/ProjectCard";
import projects from "../data/projects";

const ProjectsPage = memo(() => {
  const [activeFilter, setActiveFilter] = useState("All");
  const [filteredProjects, setFilteredProjects] = useState([]);

  // Memoize categories calculation to prevent unnecessary re-computation
  const categories = useMemo(
    () => ["All", ...new Set(projects.map((project) => project.category))],
    []
  );

  // Memoize featured projects calculation
  const featuredProjects = useMemo(
    () => projects.filter((project) => project.featured),
    []
  );

  // Optimize filter handler with useCallback
  const handleFilterClick = useCallback((category) => {
    setActiveFilter(category);
  }, []);

  useEffect(() => {
    // Filter projects based on the active filter
    if (activeFilter === "All") {
      setFilteredProjects(projects);
    } else {
      setFilteredProjects(
        projects.filter((project) => project.category === activeFilter)
      );
    }
  }, [activeFilter]);

  return (
    <main>
      <div className="o-wrapper u-pt-xl">
        <div className="projects-page">
          <h1 className="u-h1" data-aos="fade-up">
            Projects
          </h1>

          {featuredProjects.length > 0 && (
            <div className="featured-projects" data-aos="fade-up">
              <div className="featured-banner">
                <div className="featured-banner__icon">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z"
                      fill="#FF4338"
                    />
                  </svg>
                </div>
                <h2 className="featured-banner__title">Featured Projects</h2>
              </div>
              <div className="projects-grid">
                {featuredProjects.map((project) => (
                  <ProjectCard key={project.id} project={project} />
                ))}
              </div>
            </div>
          )}

          <div className="projects-filter" data-aos="fade-up">
            {categories.map((category) => (
              <button
                key={category}
                className={`filter-button ${
                  activeFilter === category ? "active" : ""
                }`}
                onClick={() => handleFilterClick(category)}
              >
                {category}
              </button>
            ))}
          </div>

          <div className="projects-grid">
            {filteredProjects.map((project) => (
              <div key={project.id} data-aos="fade-up">
                <ProjectCard project={project} />
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <p className="no-results" data-aos="fade-up">
              No projects found for this category. Please try another filter.
            </p>
          )}
        </div>
      </div>
    </main>
  );
});

ProjectsPage.displayName = "ProjectsPage";

export default ProjectsPage;
