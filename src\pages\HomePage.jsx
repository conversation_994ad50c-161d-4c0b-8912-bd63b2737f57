import React, { useEffect, useState, memo, useMemo } from "react";
import { Link } from "react-router-dom";
import "./HomePage.css";
import CTACard from "../components/CTACard/CTACard";
import projects from "../data/projects";
import teamMembers from "../data/teamMembers";
import {
  INTRO_VIDEOS,
  CONTENT_CONFIG,
  ANIMATION_CONFIG,
} from "../config/constants";
import {
  getRandomItem,
  getFeaturedProjects,
  getTeamMembersByRole,
} from "../utils/helpers";
import { useIntersectionObserver } from "../hooks/index";

const HomePage = memo(() => {
  const [isIntroComplete, setIsIntroComplete] = useState(false);
  const [displayedProjects, setDisplayedProjects] = useState([]);

  // Memoize founders calculation with helper function
  const founders = useMemo(
    () =>
      getTeamMembersByRole(
        team<PERSON><PERSON><PERSON>,
        "director",
        CONTENT_CONFIG.homepage.foundersToShow
      ),
    []
  );

  // Memoize featured projects calculation
  const featuredProjectsList = useMemo(() => {
    const featuredProjs = getFeaturedProjects(projects);
    return featuredProjs.length >= CONTENT_CONFIG.homepage.projectsToShow
      ? featuredProjs
      : projects;
  }, []);

  // Filter featured projects or just take the first three if none are marked as featured
  useEffect(() => {
    setDisplayedProjects(
      featuredProjectsList.slice(0, CONTENT_CONFIG.homepage.projectsToShow)
    );
  }, [featuredProjectsList]);

  // Simulate intro animation completion
  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsIntroComplete(true);
    }, CONTENT_CONFIG.homepage.introVideoDuration);
    return () => clearTimeout(timeout);
  }, []);

  // Memoize intersection observer options to prevent recreation
  const intersectionOptions = useMemo(
    () => ({
      threshold: 0.3,
    }),
    []
  );

  // Optimize intersection observer with custom hook
  const [textAnimationRef, isTextVisible] =
    useIntersectionObserver(intersectionOptions);

  // Animate text underlines when visible
  useEffect(() => {
    if (isTextVisible) {
      const currentRef = textAnimationRef.current;
      if (currentRef) {
        const underlineElements = currentRef.querySelectorAll("em[data-svg]");
        underlineElements.forEach((el) => {
          setTimeout(() => {
            el.setAttribute("data-svg-animated", "true");
          }, ANIMATION_CONFIG.delays.textAnimation);
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTextVisible]);

  return (
    <main>
      <div className="c-intro">
        <div className="c-intro__motion">
          <video
            muted
            autoPlay
            playsInline={true}
            controls={false}
            preload="metadata"
            loading="lazy"
            src="/videos/hero.mp4"
            aria-hidden="true"
          >
            <source src="/videos/hero.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
        <div className={`c-intro__outro ${isIntroComplete ? "visible" : ""}`}>
          <div>Designing spaces that inspire!</div>
        </div>
        <div className="c-intro__macron">
          <div className="c-intro__macron-play">
            <svg
              width="49"
              height="25"
              viewBox="0 0 49 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.825 18H3.7V12.75H7.1C10.85 12.75 13.275 9.925 13.275 6.625C13.275 3.325 10.85 0.499999 7.1 0.499999H0.825V18ZM7.1 10.175H3.7V3.075H7.1C9.1 3.075 10.4 4.7 10.4 6.625C10.4 8.55 9.1 10.175 7.1 10.175ZM15.7176 18H18.4426V0.499999H15.7176V18ZM30.7896 6.35V7.925C30.0646 6.75 28.5396 6.05 27.1896 6.05C23.6396 6.05 21.2146 8.875 21.2146 12.175C21.2146 15.475 23.6396 18.3 27.1896 18.3C28.5396 18.3 30.0646 17.6 30.7896 16.425V18H33.5146V6.35H30.7896ZM27.3396 15.875C25.2896 15.875 23.9396 14.175 23.9396 12.175C23.9396 10.175 25.2896 8.475 27.3396 8.475C29.4396 8.475 30.7896 10.175 30.7896 12.175C30.7896 14.175 29.4396 15.875 27.3396 15.875ZM42.4031 24.125C44.2031 24.125 45.6031 23.7 46.5531 22.85C47.5031 22 48.0031 20.725 48.0031 19V6.3H45.2781V12.325C45.2781 14.325 44.3781 15.85 42.4781 15.85C40.5281 15.85 39.9531 14.325 39.9531 12.325V6.3H37.2281V13.225C37.2281 14.825 37.4031 15.8 37.9781 16.675C38.6281 17.65 39.8031 18.25 41.4781 18.25C43.2031 18.25 44.5531 17.575 45.2781 16.325V19C45.2781 21.175 44.1031 21.775 42.4281 21.775C41.0031 21.775 39.8031 20.95 39.6781 19.275H36.9281C36.9531 20.875 37.5531 22.075 38.5281 22.875C39.5031 23.675 40.8531 24.075 42.4031 24.075V24.125Z"
                fill="white"
              />
            </svg>
          </div>
          <div className="c-intro__loader">
            <div className="c-intro__stage c-intro__scene-one">
              <svg
                width="72"
                height="56"
                viewBox="0 0 72 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M36 10.5659L25 45.4338H31L33.5 37.8489H46.5L49 45.4338H55L44 10.5659H36Z"
                  fill="#FF4338"
                />
                {/* Rest of SVG paths ... (truncated for brevity) */}
              </svg>
            </div>
          </div>
        </div>

        <div className="c-intro__cue has_movement">
          Scroll
          <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.00037 12.6331L6.99966 0.460298L8.99936 0.460298L9.00007 12.6317L14.3635 7.26832L15.7777 8.68253L7.99951 16.4607L0.221337 8.68254L1.63555 7.26832L7.00037 12.6331Z"
              fill="black"
            />
          </svg>
        </div>
      </div>

      <div className="u-light-bg u-pt-xl">
        <div className="o-wrapper">
          <div className="o-layout">
            <div
              className="o-layout__item u-1/2@desktop"
              data-aos="fade-up"
              ref={textAnimationRef}
            >
              <h1 className="u-h4 u-mb-l c-intro-text">
                <p>
                  We believe in{" "}
                  <em data-svg="scribble" style={{ color: "#ff4338" }}>
                    meaningful, thoughtful design
                  </em>
                  ; refined and responsive to the rhythms of everyday life.
                  Rooted in how spaces shape experience, our architecture values{" "}
                  <em data-svg="straight" style={{ color: "#ff4338" }}>
                    light, movement, and material
                  </em>
                  .
                </p>
                <p>
                  With quiet clarity, we create environments where
                  <em data-svg="scribble" style={{ color: "#ff4338" }}>
                    function meets feeling
                  </em>{" "}
                  and design becomes a natural part of living.
                </p>
              </h1>
            </div>
          </div>

          {/* About Us Section */}
          <div className="about-section" data-aos="fade-up">
            <div className="about-header">
              <h2 className="about-title">About Us</h2>
              <div className="about-subtitle">Leadership</div>
            </div>

            <div className="leadership-grid">
              {founders.map((founder, index) => (
                <div
                  key={founder.id}
                  className="leader-card"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <div className="leader-image-container">
                    <div className="leader-image">
                      <img src={founder.imageUrl} alt={founder.name} />
                    </div>
                  </div>
                  <div className="leader-info">
                    <h3 className="leader-name">{founder.name}</h3>
                    <p className="leader-role">{founder.role}</p>
                    <p className="leader-description">{founder.bio}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Featured Projects Section */}
          <div className="featured-projects-section">
            <div className="projects-header" data-aos="fade-up">
              <div className="projects-subtitle">Portfolio</div>
              <h2 className="projects-title">Featured Projects</h2>
            </div>

            <div className="projects-grid">
              {displayedProjects.map((project, index) => (
                <div
                  key={project.id}
                  className="project-item"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <Link
                    to={`/projects/${project.slug}`}
                    className="project-link"
                  >
                    <div className="project-image-container">
                      <img src={project.imageUrl} alt={project.title} />
                      <div className="project-overlay">
                        <div className="project-details">
                          <h3 className="project-name">{project.title}</h3>
                          <span className="project-type">Architecture</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            <div className="projects-footer" data-aos="fade-up">
              <Link to="/projects" className="view-all-btn">
                <span>View All Projects</span>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M5 12H19M19 12L12 5M19 12L12 19"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Link>
            </div>
          </div>

          <div className="o-layout c-offset-grid">
            <div
              className="o-layout__item u-6/12 u-3/12@tablet u-pull-up-l u-mb-m"
              data-aos="fade-up"
            ></div>
          </div>
        </div>

        <div className="u-half-bg">
          <div className="o-wrapper">
            <div className="o-layout o-layout--xlarge c-cta-card__wrap">
              <div className="o-layout__item u-1/2@tablet" data-aos="fade-up">
                <CTACard
                  title="Projects"
                  link="/projects"
                  imageUrl="https://images.unsplash.com/photo-1545958339-7ce3eea06a78?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80"
                />
              </div>
              <div
                className="o-layout__item u-1/2@tablet"
                data-aos="fade-up"
                data-aos-delay="200"
              >
                <CTACard
                  title="Studio"
                  link="/studio"
                  imageUrl="https://images.unsplash.com/photo-1577210097854-32d95191a1e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  color="red"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
});

HomePage.displayName = "HomePage";

export default HomePage;

