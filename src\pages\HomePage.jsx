import React, { useEffect, useState, memo, useMemo } from "react";
import { Link } from "react-router-dom";
import "./HomePage.css";
import CTACard from "../components/CTACard/CTACard";
import projects from "../data/projects";
import {
  INTRO_VIDEOS,
  CONTENT_CONFIG,
  ANIMATION_CONFIG,
} from "../config/constants";
import { getRandomItem, getFeaturedProjects } from "../utils/helpers";
import { useIntersectionObserver } from "../hooks/index";

const HomePage = memo(() => {
  const [isIntroComplete, setIsIntroComplete] = useState(false);
  const [displayedProjects, setDisplayedProjects] = useState([]);

  // Add homepage class to body for CSS targeting
  useEffect(() => {
    document.body.classList.add("homepage");
    return () => {
      document.body.classList.remove("homepage");
    };
  }, []);

  // Memoize featured projects calculation
  const featuredProjectsList = useMemo(() => {
    const featuredProjs = getFeaturedProjects(projects);
    return featuredProjs.length >= CONTENT_CONFIG.homepage.projectsToShow
      ? featuredProjs
      : projects;
  }, []);

  // Filter featured projects or just take the first three if none are marked as featured
  useEffect(() => {
    setDisplayedProjects(
      featuredProjectsList.slice(0, CONTENT_CONFIG.homepage.projectsToShow)
    );
  }, [featuredProjectsList]);

  // Simulate intro animation completion
  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsIntroComplete(true);
    }, CONTENT_CONFIG.homepage.introVideoDuration);
    return () => clearTimeout(timeout);
  }, []);

  // Memoize intersection observer options to prevent recreation
  const intersectionOptions = useMemo(
    () => ({
      threshold: 0.3,
    }),
    []
  );

  // Optimize intersection observer with custom hook
  const [textAnimationRef, isTextVisible] =
    useIntersectionObserver(intersectionOptions);

  // Animate text underlines when visible
  useEffect(() => {
    if (isTextVisible) {
      const currentRef = textAnimationRef.current;
      if (currentRef) {
        const underlineElements = currentRef.querySelectorAll("em[data-svg]");
        underlineElements.forEach((el) => {
          setTimeout(() => {
            el.setAttribute("data-svg-animated", "true");
          }, ANIMATION_CONFIG.delays.textAnimation);
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTextVisible]);

  return (
    <main>
      <div className="c-intro">
        <div className="c-intro__motion">
          <video
            muted
            autoPlay
            loop
            playsInline={true}
            controls={false}
            preload="metadata"
            loading="lazy"
            src="/videos/hero.mp4"
            aria-hidden="true"
          >
            <source src="/videos/hero.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
        <div className={`c-intro__outro ${isIntroComplete ? "visible" : ""}`}>
          <div className="c-intro__content">
            <div className="c-intro__hero-text">
              Meaningful
              <br />
              by Design.
            </div>
            <p className="c-intro__description">
              We believe in meaningful, thoughtful design;
              <br />
              refined and responsive to the rhythms of everyday life.
              <br />
              Rooted in how spaces shape experience, our architecture
              <br />
              values light, movement, and material. With quiet clarity,
              <br />
              we create environments where function meets feeling and
              <br />
              design becomes a natural part of living.
            </p>
          </div>
        </div>

        <div className="c-intro__cue has_movement">
          Scroll
          <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.00037 12.6331L6.99966 0.460298L8.99936 0.460298L9.00007 12.6317L14.3635 7.26832L15.7777 8.68253L7.99951 16.4607L0.221337 8.68254L1.63555 7.26832L7.00037 12.6331Z"
              fill="black"
            />
          </svg>
        </div>
      </div>

      <div className="u-light-bg u-pt-xl">
        <div className="o-wrapper">
          <div className="o-layout">
            <div
              className="o-layout__item u-1/2@desktop"
              data-aos="fade-up"
              ref={textAnimationRef}
            >
              <div className="c-story-section">
                <div className="c-story-header">
                  <div className="c-story-ellipse">
                    <img src="/elements/Ellipse 1.png" alt="" />
                  </div>
                  <h2 className="c-story-title">Two Minds, One Rhythm</h2>
                </div>

                <div className="c-story-content">
                  <div className="c-story-text">
                    <p className="c-story-paragraph">
                      What began as a shared curiosity between two architecture
                      students has grown into Auften. A studio shaped by
                      passion, purpose, and meaningful experiences.
                    </p>

                    <p className="c-story-paragraph">
                      We approach every project with open eyes and grounded
                      hearts, believing that great design is about more than
                      just structure. It's about emotion, collaboration, and
                      creating spaces that resonate with life.
                    </p>

                    <p className="c-story-paragraph">
                      What drives us? The joy of problem-solving, the thrill of
                      the process, and the magic that unfolds when design meets
                      intention. Here's to more light, more craft, and many more
                      stories to tell.
                    </p>
                  </div>

                  <div className="c-story-founder-image">
                    <img src="/founder.png" alt="Auften Founders" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Featured Projects Section */}
          <div className="featured-projects-section">
            <div className="projects-header" data-aos="fade-up">
              <h2 className="projects-title">Our Projects</h2>
            </div>

            <div className="projects-grid">
              {displayedProjects.map((project, index) => (
                <div
                  key={project.id}
                  className="project-item"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <Link
                    to={`/projects/${project.slug}`}
                    className="project-link"
                  >
                    <div className="project-image-container">
                      <img src={project.imageUrl} alt={project.title} />
                      <div className="project-overlay">
                        <div className="project-details">
                          <h3 className="project-name">{project.title}</h3>
                          <span className="project-type">Architecture</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            <div className="projects-footer" data-aos="fade-up">
              <Link to="/projects" className="view-all-btn">
                <span>View All Projects</span>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M5 12H19M19 12L12 5M19 12L12 19"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Link>
            </div>
          </div>

          <div className="o-layout c-offset-grid">
            <div
              className="o-layout__item u-6/12 u-3/12@tablet u-pull-up-l u-mb-m"
              data-aos="fade-up"
            ></div>
          </div>
        </div>

        <div className="u-half-bg">
          <div className="o-wrapper">
            <div className="o-layout o-layout--xlarge c-cta-card__wrap">
              <div className="o-layout__item u-1/2@tablet" data-aos="fade-up">
                <CTACard
                  title="Projects"
                  link="/projects"
                  imageUrl="https://images.unsplash.com/photo-1545958339-7ce3eea06a78?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80"
                />
              </div>
              <div
                className="o-layout__item u-1/2@tablet"
                data-aos="fade-up"
                data-aos-delay="200"
              >
                <CTACard
                  title="Studio"
                  link="/studio"
                  imageUrl="https://images.unsplash.com/photo-1577210097854-32d95191a1e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  color="red"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
});

HomePage.displayName = "HomePage";

export default HomePage;
