// Utility functions for the Auften website

/**
 * Animation and UI Utilities
 */

/**
 * Generates a random integer between min and max (inclusive)
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Random integer
 */
export const getRandomInt = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Selects a random item from an array
 * @param {Array} array - Array to select from
 * @returns {*} Random item from array
 */
export const getRandomItem = (array) => {
  if (!Array.isArray(array) || array.length === 0) {
    return null;
  }
  return array[getRandomInt(0, array.length - 1)];
};

/**
 * Debounces a function call
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Throttles a function call
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, delay) => {
  let inThrottle;
  return (...args) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), delay);
    }
  };
};

/**
 * Data Processing Utilities
 */

/**
 * Filters projects by category
 * @param {Array} projects - Array of project objects
 * @param {string} category - Category to filter by
 * @returns {Array} Filtered projects
 */
export const filterProjectsByCategory = (projects, category) => {
  if (!Array.isArray(projects)) {
    return [];
  }

  if (!category || category === "All") {
    return projects;
  }

  return projects.filter((project) => project.category === category);
};

/**
 * Gets unique categories from projects array
 * @param {Array} projects - Array of project objects
 * @returns {Array} Unique categories with 'All' prepended
 */
export const getProjectCategories = (projects) => {
  if (!Array.isArray(projects)) {
    return ["All"];
  }

  const categories = [...new Set(projects.map((project) => project.category))];
  return ["All", ...categories];
};

/**
 * Filters featured projects
 * @param {Array} projects - Array of project objects
 * @returns {Array} Featured projects
 */
export const getFeaturedProjects = (projects) => {
  if (!Array.isArray(projects)) {
    return [];
  }

  return projects.filter((project) => project.featured === true);
};

/**
 * Gets team members by role
 * @param {Array} teamMembers - Array of team member objects
 * @param {string} role - Role to filter by (e.g., 'Director')
 * @param {number} limit - Maximum number of members to return
 * @returns {Array} Filtered team members
 */
export const getTeamMembersByRole = (teamMembers, role, limit = null) => {
  if (!Array.isArray(teamMembers)) {
    return [];
  }

  const filtered = teamMembers.filter(
    (member) =>
      member.role && member.role.toLowerCase().includes(role.toLowerCase())
  );

  return limit ? filtered.slice(0, limit) : filtered;
};

/**
 * URL and Navigation Utilities
 */

/**
 * Generates a URL-friendly slug from a string
 * @param {string} text - Text to convert to slug
 * @returns {string} URL-friendly slug
 */
export const generateSlug = (text) => {
  if (!text || typeof text !== "string") {
    return "";
  }

  return text
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
    .trim();
};

/**
 * Checks if a URL is external
 * @param {string} url - URL to check
 * @returns {boolean} True if external URL
 */
export const isExternalUrl = (url) => {
  if (!url || typeof url !== "string") {
    return false;
  }

  return (
    url.startsWith("http://") ||
    url.startsWith("https://") ||
    url.startsWith("//")
  );
};

/**
 * Validation Utilities
 */

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email format
 */
export const isValidEmail = (email) => {
  if (!email || typeof email !== "string") {
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates phone number format
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid phone format
 */
export const isValidPhone = (phone) => {
  if (!phone || typeof phone !== "string") {
    return false;
  }

  // Basic phone validation - adjust regex as needed for specific formats
  const phoneRegex = /^[+]?[\d\s-()]{10,}$/;
  return phoneRegex.test(phone);
};

/**
 * Performance Utilities
 */

/**
 * Creates an intersection observer for lazy loading
 * @param {Function} callback - Callback function when element intersects
 * @param {Object} options - Intersection observer options
 * @returns {IntersectionObserver} Intersection observer instance
 */
export const createIntersectionObserver = (callback, options = {}) => {
  const defaultOptions = {
    threshold: 0.1,
    rootMargin: "50px",
  };

  const observerOptions = { ...defaultOptions, ...options };

  return new IntersectionObserver(callback, observerOptions);
};

/**
 * Preloads an image
 * @param {string} src - Image source URL
 * @returns {Promise} Promise that resolves when image is loaded
 */
export const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    if (!src) {
      reject(new Error("Image source is required"));
      return;
    }

    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
    img.src = src;
  });
};

/**
 * Animation Utilities
 */

/**
 * Adds CSS class with animation delay
 * @param {HTMLElement} element - Element to animate
 * @param {string} className - CSS class to add
 * @param {number} delay - Delay in milliseconds
 */
export const addClassWithDelay = (element, className, delay = 0) => {
  if (!element || !className) {
    return;
  }

  setTimeout(() => {
    element.classList.add(className);
  }, delay);
};

/**
 * Smoothly scrolls to an element
 * @param {HTMLElement|string} target - Element or selector to scroll to
 * @param {Object} options - Scroll options
 */
export const smoothScrollTo = (target, options = {}) => {
  const element =
    typeof target === "string" ? document.querySelector(target) : target;

  if (!element) {
    return;
  }

  const defaultOptions = {
    behavior: "smooth",
    block: "start",
    inline: "nearest",
  };

  const scrollOptions = { ...defaultOptions, ...options };
  element.scrollIntoView(scrollOptions);
};

/**
 * Local Storage Utilities
 */

/**
 * Safely gets item from localStorage
 * @param {string} key - Storage key
 * @param {*} defaultValue - Default value if key doesn't exist
 * @returns {*} Stored value or default value
 */
export const getStorageItem = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn(`Error reading localStorage key "${key}":`, error);
    return defaultValue;
  }
};

/**
 * Safely sets item in localStorage
 * @param {string} key - Storage key
 * @param {*} value - Value to store
 * @returns {boolean} True if successful
 */
export const setStorageItem = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.warn(`Error setting localStorage key "${key}":`, error);
    return false;
  }
};

/**
 * Device Detection Utilities
 */

/**
 * Checks if device is mobile
 * @returns {boolean} True if mobile device
 */
export const isMobile = () => {
  return window.innerWidth <= 768;
};

/**
 * Checks if device is tablet
 * @returns {boolean} True if tablet device
 */
export const isTablet = () => {
  return window.innerWidth > 768 && window.innerWidth <= 1024;
};

/**
 * Checks if device is desktop
 * @returns {boolean} True if desktop device
 */
export const isDesktop = () => {
  return window.innerWidth > 1024;
};

/**
 * Gets current device type
 * @returns {string} Device type ('mobile', 'tablet', 'desktop')
 */
export const getDeviceType = () => {
  if (isMobile()) return "mobile";
  if (isTablet()) return "tablet";
  return "desktop";
};
