/* Spinner styles */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(255, 67, 56, 0.1);
  border-top-color: #ff4338;
  animation: spin 1s infinite linear;
}

.spinner-container.small .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner-container.large .spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
