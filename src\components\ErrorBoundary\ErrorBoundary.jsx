import React, { Component } from "react";
import PropTypes from "prop-types";
import "./ErrorBoundary.css";
import { performanceMonitor } from "../../utils/performance";

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Enhanced error logging
    const enhancedError = {
      id: errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.props.userId || "anonymous",
    };

    // Log to console in development
    if (process.env.NODE_ENV === "development") {
      console.error("Error caught by ErrorBoundary:", enhancedError);
    }

    // Track performance impact
    performanceMonitor.startTiming(`error_recovery_${errorId}`);

    // Store error details in state
    this.setState({
      error: error,
      errorInfo: errorInfo,
      errorId: errorId,
    });

    // Report to error tracking service (if configured)
    if (this.props.onError) {
      this.props.onError(enhancedError);
    }

    // Optional: Send to external error monitoring service
    if (process.env.NODE_ENV === "production" && window.reportError) {
      window.reportError(enhancedError);
    }
  }

  handleRetry = () => {
    // Track retry attempt
    if (this.state.errorId) {
      performanceMonitor.endTiming(`error_recovery_${this.state.errorId}`);
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleRefresh = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI from props
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI
      return (
        <div className="error-boundary" role="alert" aria-live="assertive">
          <div className="error-container">
            <div className="error-icon" aria-hidden="true">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>

            <h2>Something went wrong</h2>
            <p>
              We apologize for the inconvenience. This error has been logged and
              our team will investigate.
            </p>

            <div className="error-actions">
              <button
                onClick={this.handleRetry}
                className="btn btn--primary"
                aria-label="Try again without refreshing the page"
              >
                Try Again
              </button>
              <button
                onClick={this.handleRefresh}
                className="btn btn--secondary"
                aria-label="Refresh the entire page"
              >
                Refresh Page
              </button>
            </div>

            {(this.props.showDetails ||
              process.env.NODE_ENV === "development") && (
              <details className="error-details">
                <summary>Technical Details</summary>
                <div className="error-details-content">
                  <p>
                    <strong>Error ID:</strong> {this.state.errorId}
                  </p>
                  <p>
                    <strong>Message:</strong>{" "}
                    {this.state.error && this.state.error.toString()}
                  </p>
                  {this.state.errorInfo && (
                    <div className="stack-trace">
                      <strong>Component Stack:</strong>
                      <pre>{this.state.errorInfo.componentStack}</pre>
                    </div>
                  )}
                  {this.state.error && this.state.error.stack && (
                    <div className="stack-trace">
                      <strong>Stack Trace:</strong>
                      <pre>{this.state.error.stack}</pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired,
  fallback: PropTypes.func,
  onError: PropTypes.func,
  showDetails: PropTypes.bool,
  userId: PropTypes.string,
};

ErrorBoundary.defaultProps = {
  showDetails: false,
};

export default ErrorBoundary;
