import React, { memo } from "react";
import { <PERSON> } from "react-router-dom";
import "./Footer.css";
import { SITE_CONFIG, ASSETS } from "../../config/constants";

const Footer = memo(() => {
  return (
    <footer className="c-footer" role="contentinfo">
      <div className="o-wrapper">
        <div className="c-footer__body">
          <div className="c-footer__body-col">
            <div className="c-footer__content">
              <div className="c-logo c-logo--footer">
                <Link
                  to="/"
                  title={`${SITE_CONFIG.name} home page`}
                  aria-label="Return to homepage"
                >
                  <img
                    src={ASSETS.logos.black}
                    alt={`${SITE_CONFIG.name} Logo`}
                    width="200"
                    height="48"
                  />
                </Link>
              </div>

              <div className="c-social u-mb-m">
                <ul className="c-social__list">
                  <li className="c-social__list-item">
                    <a
                      href={SITE_CONFIG.social.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      title="View our Instagram page"
                      aria-label="Follow us on Instagram (opens in new tab)"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10 7C9.20435 7 8.44129 7.31607 7.87868 7.87868C7.31607 8.44129 7 9.20435 7 10C7 10.7956 7.31607 11.5587 7.87868 12.1213C8.44129 12.6839 9.20435 13 10 13C10.7956 13 11.5587 12.6839 12.1213 12.1213C12.6839 11.5587 13 10.7956 13 10C13 9.20435 12.6839 8.44129 12.1213 7.87868C11.5587 7.31607 10.7956 7 10 7ZM10 5C11.3261 5 12.5979 5.52678 13.5355 6.46447C14.4732 7.40215 15 8.67392 15 10C15 11.3261 14.4732 12.5979 13.5355 13.5355C12.5979 14.4732 11.3261 15 10 15C8.67392 15 7.40215 14.4732 6.46447 13.5355C5.52678 12.5979 5 11.3261 5 10C5 8.67392 5.52678 7.40215 6.46447 6.46447C7.40215 5.52678 8.67392 5 10 5ZM16.5 4.75C16.5 5.08152 16.3683 5.39946 16.1339 5.63388C15.8995 5.8683 15.5815 6 15.25 6C14.9185 6 14.6005 5.8683 14.3661 5.63388C14.1317 5.39946 14 5.08152 14 4.75C14 4.41848 14.1317 4.10054 14.3661 3.86612C14.6005 3.6317 14.9185 3.5 15.25 3.5C15.5815 3.5 15.8995 3.6317 16.1339 3.86612C16.3683 4.10054 16.5 4.41848 16.5 4.75ZM10 2C7.526 2 7.122 2.007 5.971 2.058C5.187 2.095 4.661 2.2 4.173 2.39C3.76462 2.53994 3.39541 2.78026 3.093 3.093C2.78001 3.3954 2.53935 3.76458 2.389 4.173C2.199 4.663 2.094 5.188 2.058 5.971C2.006 7.075 2 7.461 2 10C2 12.474 2.007 12.878 2.058 14.029C2.095 14.812 2.2 15.339 2.389 15.826C2.559 16.261 2.759 16.574 3.091 16.906C3.428 17.242 3.741 17.443 4.171 17.609C4.665 17.8 5.191 17.906 5.971 17.942C7.075 17.994 7.461 18 10 18C12.474 18 12.878 17.993 14.029 17.942C14.811 17.905 15.338 17.8 15.826 17.611C16.2338 17.4603 16.6027 17.2205 16.906 16.909C17.243 16.572 17.444 16.259 17.61 15.829C17.8 15.336 17.906 14.809 17.942 14.029C17.994 12.925 18 12.539 18 10C18 7.526 17.993 7.122 17.942 5.971C17.905 5.189 17.8 4.661 17.61 4.173C17.4593 3.765 17.2191 3.39596 16.907 3.093C16.6047 2.77985 16.2355 2.53917 15.827 2.389C15.337 2.199 14.811 2.094 14.029 2.058C12.925 2.006 12.539 2 10 2ZM10 0C12.717 0 13.056 0.00999994 14.122 0.0599999C15.187 0.11 15.912 0.277 16.55 0.525C17.21 0.779 17.766 1.123 18.322 1.678C18.8305 2.1779 19.224 2.78259 19.475 3.45C19.722 4.087 19.89 4.813 19.94 5.878C19.987 6.944 20 7.283 20 10C20 12.717 19.99 13.056 19.94 14.122C19.89 15.187 19.722 15.912 19.475 16.55C19.2247 17.2178 18.8311 17.8226 18.322 18.322C17.822 18.8303 17.2173 19.2238 16.55 19.475C15.913 19.722 15.187 19.89 14.122 19.94C13.056 19.987 12.717 20 10 20C7.283 20 6.944 19.99 5.878 19.94C4.813 19.89 4.088 19.722 3.45 19.475C2.78233 19.2245 2.17753 18.8309 1.678 18.322C1.16941 17.8222 0.775931 17.2175 0.525 16.55C0.277 15.913 0.11 15.187 0.0599999 14.122C0.0129999 13.056 0 12.717 0 10C0 7.283 0.00999994 6.944 0.0599999 5.878C0.11 4.812 0.277 4.088 0.525 3.45C0.775236 2.78218 1.1688 2.17732 1.678 1.678C2.17767 1.16923 2.78243 0.775729 3.45 0.525C4.088 0.277 4.812 0.11 5.878 0.0599999C6.944 0.0129999 7.283 0 10 0Z"
                          fill="#000000"
                        />
                      </svg>
                    </a>
                  </li>
                  <li className="c-social__list-item">
                    <a
                      href={SITE_CONFIG.social.whatsapp}
                      target="_blank"
                      rel="noopener noreferrer"
                      title="Contact us on WhatsApp"
                      aria-label="Contact us on WhatsApp (opens in new tab)"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.05 2.91C16.18 2.03 15.15 1.33 14.02 0.86C12.89 0.39 11.68 0.16 10.45 0.18C4.85 0.18 0.32 4.71 0.32 10.31C0.32 12.11 0.78 13.87 1.66 15.43L0.24 19.76L4.69 18.36C6.2 19.18 7.91 19.62 9.65 19.62H9.65C15.25 19.62 19.78 15.09 19.78 9.49C19.78 6.78 18.72 4.24 17.05 2.91ZM10.45 18.06C8.92 18.06 7.42 17.64 6.12 16.85L5.82 16.67L2.53 17.53L3.4 14.33L3.2 14.02C2.35 12.68 1.9 11.12 1.9 9.52C1.9 5.58 5.12 2.36 9.06 2.36C10.98 2.36 12.8 3.12 14.18 4.5C15.56 5.88 16.32 7.7 16.32 9.62C16.32 13.56 13.1 16.78 9.16 16.78L10.45 18.06ZM13.09 11.29C12.87 11.18 11.79 10.65 11.59 10.57C11.39 10.49 11.25 10.45 11.11 10.67C10.97 10.89 10.56 11.39 10.44 11.53C10.32 11.67 10.2 11.69 9.98 11.58C9.76 11.47 9.03 11.22 8.15 10.43C7.47 9.81 7.01 9.05 6.89 8.83C6.77 8.61 6.87 8.5 6.98 8.39C7.08 8.29 7.2 8.13 7.31 8.01C7.42 7.89 7.46 7.81 7.54 7.67C7.62 7.53 7.58 7.41 7.52 7.3C7.46 7.19 7.01 6.11 6.83 5.67C6.65 5.25 6.47 5.3 6.35 5.29C6.23 5.28 6.09 5.28 5.95 5.28C5.81 5.28 5.59 5.34 5.39 5.56C5.19 5.78 4.62 6.31 4.62 7.39C4.62 8.47 5.41 9.51 5.52 9.65C5.63 9.79 7.01 11.94 9.12 12.9C9.62 13.13 10.01 13.27 10.31 13.37C10.81 13.53 11.27 13.51 11.63 13.45C12.03 13.38 12.87 12.92 13.05 12.42C13.23 11.92 13.23 11.5 13.17 11.39C13.11 11.28 12.97 11.22 12.75 11.11L13.09 11.29Z"
                          fill="#000000"
                        />
                      </svg>
                    </a>
                  </li>
                  <li className="c-social__list-item">
                    <a
                      href={SITE_CONFIG.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      title="View our LinkedIn page"
                      aria-label="Follow us on LinkedIn (opens in new tab)"
                    >
                      <svg
                        width="20"
                        height="18"
                        viewBox="0 0 20 18"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4 2.001C3.99974 2.53143 3.78877 3.04004 3.41351 3.41492C3.03825 3.78981 2.52943 4.00027 1.999 4C1.46857 3.99974 0.959965 3.78877 0.585079 3.41351C0.210194 3.03825 -0.000264966 2.52943 2.50361e-07 1.999C0.000265467 1.46857 0.211233 0.959965 0.586494 0.585079C0.961754 0.210194 1.47057 -0.000264966 2.001 2.50361e-07C2.53143 0.000265467 3.04004 0.211233 3.41492 0.586494C3.78981 0.961754 4.00027 1.47057 4 2.001ZM4.06 5.481H0.0600002V18.001H4.06V5.481ZM10.38 5.481H6.4V18.001H10.34V11.431C10.34 7.771 15.11 7.431 15.11 11.431V18.001H19.06V10.071C19.06 3.901 12 4.131 10.34 7.161L10.38 5.481Z"
                          fill="#000000"
                        />
                      </svg>
                    </a>
                  </li>
                </ul>
              </div>

              <p
                className="u-mb-m"
                style={{
                  fontFamily: "Outfit, sans-serif",
                  fontWeight: "500",
                }}
              >
                Join us
                <br />
                <a
                  href="mailto:<EMAIL>"
                  aria-label="Email us for job opportunities"
                  style={{ fontWeight: "500" }}
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          <div className="c-footer__body-col c-footer__contact-info">
            <div className="c-contact-info" style={{ textAlign: "center" }}>
              <p
                style={{
                  fontFamily: "Outfit, sans-serif",
                  fontWeight: "500",
                  margin: "0 0 0.5rem 0",
                  fontSize: "1rem",
                }}
              >
                <a
                  href="tel:+918078448709"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  +91 807 8448 709
                </a>
              </p>
              <p
                style={{
                  fontFamily: "Outfit, sans-serif",
                  fontWeight: "500",
                  margin: "0 0 0.5rem 0",
                  fontSize: "1rem",
                }}
              >
                <a
                  href="tel:+917034496930"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  +91 7034 4969 30
                </a>
              </p>
              <p
                style={{
                  fontFamily: "Outfit, sans-serif",
                  fontWeight: "500",
                  margin: "0 0 1rem 0",
                  fontSize: "1rem",
                }}
              >
                <a
                  href="mailto:<EMAIL>"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <EMAIL>
                </a>
              </p>
              <p
                style={{
                  fontFamily: "Outfit, sans-serif",
                  fontWeight: "400",
                  margin: "0",
                  fontSize: "0.9rem",
                  lineHeight: "1.4",
                }}
              >
                Kowdiar, Trivandrum
                <br />
                Kerala
              </p>
            </div>
          </div>
        </div>
        <div className="c-footer__info u-mb-m">
          <div className="c-footer__info-col c-footer__accred">
            <svg
              width="80"
              height="69"
              viewBox="0 0 80 69"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_714_2486)">
                <path
                  d="M20.9514 50.1273L20.0265 43.7424C19.8487 42.4724 19.742 41.6611 19.6708 41.0261H19.6353C19.5285 41.5553 19.3507 42.2255 19.1017 43.0369L16.6828 50.1626H15.0465L12.77 43.4954C12.4854 42.6488 12.2008 41.7316 12.0586 41.0261C11.9874 41.9433 11.8807 42.8252 11.7384 43.9188L10.9914 50.1979H9.42627L11.0626 39.1212H12.9478L15.2955 46.1059C15.5801 46.9172 15.7935 47.658 15.9358 48.293H15.9714C16.1493 47.5169 16.2915 46.9878 16.5761 46.1764L18.995 39.0859H20.8803L22.5877 50.1626H20.9514V50.1273Z"
                  fill="black"
                />
                {/* Rest of SVG path content... (truncated for brevity) */}
              </g>
              <defs>
                <clipPath id="clip0_714_2486">
                  <rect width="80" height="69" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div className="c-footer__info-col">
            <ul className="c-footer__info-nav">
              <li className="menu-item">
                <Link to="/privacy-policy">Privacy Policy</Link>
              </li>
              <li>
                <div className="c-footer__webforge">
                  <a
                    href="https://bit.ly/webforgestudio"
                    target="_blank"
                    rel="noopener noreferrer"
                    title="Built by WebForge Studio"
                  >
                    <span className="webforge-w">W</span>
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
});

Footer.displayName = "Footer";

export default Footer;
